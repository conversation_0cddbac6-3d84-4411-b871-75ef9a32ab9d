apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    dbp-description: "646565"
    dbp-engine-type: sglang
    dbp-name: "646565"
    dbp-replicas: "1"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    serving.kserve.io/deploymentMode: RawDeployment
    serving.kserve.io/enable-nodeport: "true"
  name: test-svc
spec:
  predictor:
    minReplicas: 1
    model:
      args:
      - --model-path=/mnt/models
      - --host=0.0.0.0
      - --served-model-name=Qwen3-0.6B
      - --tp-size=1
      - --port=80
      env:
      - name: PYTHONFAULTHANDLER
        value: "0"
      modelFormat:
        name: sglang
      name: ""
      ports:
      - containerPort: 80
        protocol: TCP
      resources:
        limits:
          cpu: "8"
          memory: 16384M
          nvidia.com/gpu: "1"
          nvidia.com/gpucores: "100"
          nvidia.com/gpumem-percentage: "100"
        requests:
          cpu: "8"
          memory: 16384M
          nvidia.com/gpu: "1"
          nvidia.com/gpucores: "100"
          nvidia.com/gpumem-percentage: "100"
      storageUri: cc://root/Qwen3-0.6B
    runtimeClassName: nvidia
    serviceAccountName: dbp-10-200-8-248-30080
