### **KServe 版本与功能可发现性 API 设计文档 (PRD)**

| **文档版本** | **修订日期** | **作者** | **修订说明** |
| :--- | :--- | :--- | :--- |
| v1.0 | 2025-07-15 | Gemini | 初始草案创建 |
| v1.1 | 2025-07-15 | Gemini | 根据决策，添加独立的 /features 端点 |

### 1. 背景与目标

#### 1.1. 问题陈述 (Problem Statement)

KServe 作为一个功能丰富的平台，其版本迭代迅速，并包含众多可通过配置启用的功能特性（例如 RawDeployment 模式、PodSpec 字段支持等）。当前，用户（包括平台管理员、SRE 和开发者）缺乏一个简单、标准化的机制来解决以下问题：

1.  **版本不透明**：无法通过 API 快速、准确地确定当前运行的 KServe 控制器是哪个版本、哪个 Git 提交构建的。
2.  **功能不可知**：无法得知当前部署的 KServe 实例启用了哪些核心功能或实验性功能。
3.  **诊断困难**：当遇到问题时，很难判断是由于版本差异还是功能开关配置不当导致的，增加了沟通和排查成本。

#### 1.2. 项目目标 (Goals & Objectives)

本项目旨在通过设计和实现一套标准化的 RESTful API，解决上述问题，达成以下核心目标：

1.  **提升系统可观测性**：提供程序化的方式查询 KServe 的精确版本信息和功能特性集。
2.  **标准化功能管理**：建立一个统一的框架来定义和暴露“功能开关”(Feature Gates)，使新功能的集成和发布更安全、更可控。
3.  **简化运维与支持**：为自动化工具（如监控系统、CI/CD 流水线）和人工排查提供可靠的数据源，降低维护成本。
4.  **对齐业界最佳实践**：参考 Prometheus、Grafana 等成熟云原生项目的做法，使 KServe 在这方面达到行业领先水平。

#### 1.3. 非目标 (Non-Goals)

为确保项目范围清晰，本次开发**不包括**以下内容：

*   通过 API 动态修改（启用/禁用）功能开关。功能开关的状态在本次迭代中是只读的，由服务启动时的配置决定。
*   为版本和功能查询提供图形用户界面 (UI)。
*   对 API 请求进行复杂的认证和授权（初版可作为公开只读接口）。

### 2. 用户故事 (User Stories)

| **角色** | **用户故事** |
| :--- | :--- |
| **平台管理员** | "作为一个平台管理员，我希望能通过调用一个 API 端点，来确认 KServe 的版本和已启用的功能列表，以便我能验证集群部署的正确性并为开发者提供准确的环境信息。" |
| **SRE/运维工程师** | "作为一个 SRE，我希望能通过 Prometheus 抓取一个包含版本信息的指标 (`metric`)，以便我能在 Grafana 仪表盘上监控整个舰队的 KServe 版本分布，并对过时的实例设置告警。" |
| **应用开发者** | "作为一个应用开发者，当我的 `InferenceService` 表现异常时，我希望能调用 `/features` API 来查询 KServe 的功能列表，以快速判断是否是某个我依赖的 Beta 功能未被管理员开启。" |
| **KServe 核心开发者** | "作为一个 KServe 开发者，我希望能有一个标准化的方式来注册新的实验性功能，并将其默认关闭，以便我能安全地将相关代码合并到主干，实现真正的持续交付。" |

### 3. 功能需求 (Functional Requirements)

#### 3.1. API 端点设计

将在 KServe Controller Manager 中内嵌一个轻量级 HTTP 服务器，暴露以下只读 (GET) 端点。

##### 3.1.1. 获取完整版本与功能信息

*   **Endpoint**: `GET /` 或 `GET /info`
*   **认证**: 无
*   **描述**: 返回一个组合对象，包含服务的静态版本信息和所有功能特性的当前状态。这是最主要的聚合信息端点。
*   **成功响应 (200 OK)**:
    ```json
    {
      "version": {
        "version": "v0.15.1",
        "gitCommit": "a4782c249b119d35970545545d2a091b738e7545",
        "buildDate": "2025-07-10T10:30:00Z",
        "goVersion": "go1.21.5"
      },
      "features": [
        {
          "name": "RawDeployment",
          "enabled": true,
          "description": "Enables support for deploying raw Kubernetes Deployments without Knative.",
          "version": "v0.10.0"
        },
        {
          "name": "PodSpecTolerations",
          "enabled": false,
          "description": "Allows setting tolerations directly in the PodSpec.",
          "version": "v0.15.0"
        }
      ]
    }
    ```

##### 3.1.2. 单独获取版本信息

*   **Endpoint**: `GET /version`
*   **认证**: 无
*   **描述**: 仅返回静态的构建版本信息。
*   **成功响应 (200 OK)**:
    ```json
    {
      "version": "v0.15.1",
      "gitCommit": "a4782c249b119d35970545545d2a091b738e7545",
      "buildDate": "2025-07-10T10:30:00Z",
      "goVersion": "go1.21.5"
    }
    ```

##### 3.1.3. 单独获取功能列表

*   **Endpoint**: `GET /features`
*   **认证**: 无
*   **描述**: 仅返回所有可用功能开关及其当前状态的列表。适用于只关心功能激活状态的客户端。
*   **成功响应 (200 OK)**:
    ```json
    [
      {
        "name": "RawDeployment",
        "enabled": true,
        "description": "Enables support for deploying raw Kubernetes Deployments without Knative.",
        "version": "v0.10.0"
      },
      {
        "name": "PodSpecTolerations",
        "enabled": false,
        "description": "Allows setting tolerations directly in the PodSpec.",
        "version": "v0.15.0"
      }
    ]
    ```

#### 3.2. 数据结构定义 (Go Structs)

建议在 `pkg/version/` 目录下定义以下数据结构。

```go
// pkg/version/version.go
type Info struct {
    Version   string `json:"version"`
    GitCommit string `json:"gitCommit"`
    BuildDate string `json:"buildDate"`
    GoVersion string `json:"goVersion"`
}

// pkg/version/features.go
type FeatureGate struct {
    Name              string `json:"name"`
    Enabled           bool   `json:"enabled"`
    Description       string `json:"description"`
    version string `json:"version"` // 标记该功能引入的版本
}
```

#### 3.3. 功能开关配置

1.  **代码内定义**：所有可用的功能开关应在代码中集中定义，包含其名称、默认状态和描述。
2.  **通过环境变量配置**：管理员可以通过环境变量来覆盖功能开关的默认状态。
    *   **环境变量格式**: `KSERVE_FEATURE_GATES`
    *   **值**: 一个以逗号分隔的 `key=value` 列表。
    *   **示例**: `KSERVE_FEATURE_GATES="RawDeployment=true,PodSpecTolerations=true"`
    *   未在此变量中提及的功能开关将保持其代码中定义的默认值。






