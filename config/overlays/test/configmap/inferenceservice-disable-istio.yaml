apiVersion: v1
kind: ConfigMap
metadata:
  name: inferenceservice-config
  namespace: kserve
data:
  ingress: |-
    { 
       "kserveIngressGateway": "kserve/kserve-ingress-gateway",
       "ingressGateway" : "knative-serving/knative-ingress-gateway",
       "localGateway" : "knative-serving/knative-local-gateway",
       "localGatewayService" : "knative-local-gateway.istio-system.svc.cluster.local",
       "ingressDomain"  : "example.com",
       "ingressClassName" : "istio",
       "domainTemplate": "{{ .Name }}-{{ .Namespace }}.{{ .IngressDomain }}",
       "urlScheme": "http",
       "disableIstioVirtualHost": true
    }
