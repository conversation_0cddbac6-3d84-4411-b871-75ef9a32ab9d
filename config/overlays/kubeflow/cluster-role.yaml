apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubeflow-kserve-admin
  labels:
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-admin: "true"
aggregationRule:
  clusterRoleSelectors:
  - matchLabels:
      rbac.authorization.kubeflow.org/aggregate-to-kubeflow-kserve-admin: "true"
rules: []
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubeflow-kserve-edit
  labels:
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-edit: "true"
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-kserve-admin: "true"
rules:
- apiGroups:
  - serving.kserve.io
  resources:
  - inferencegraphs
  - inferenceservices
  - servingruntimes
  - trainedmodels
  verbs:
  - get
  - list
  - watch
  - create
  - delete
  - deletecollection
  - patch
  - update
- apiGroups:
  - serving.knative.dev
  resources:
  - services
  - services/status
  - routes
  - routes/status
  - configurations
  - configurations/status
  - revisions
  - revisions/status
  verbs:
  - get
  - list
  - watch
  - create
  - delete
  - deletecollection
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubeflow-kserve-view
  labels:
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-view: "true"
rules:
- apiGroups:
  - serving.kserve.io
  resources:
  - inferencegraphs
  - inferenceservices
  - servingruntimes
  - trainedmodels
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - serving.knative.dev
  resources:
  - services
  - services/status
  - routes
  - routes/status
  - configurations
  - configurations/status
  - revisions
  - revisions/status
  verbs:
  - get
  - list
