---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kserve-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - get
  - update
- apiGroups:
  - ""
  resources:
  - events
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - namespaces
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  - serviceaccounts
  verbs:
  - get
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - mutatingwebhookconfigurations
  - validatingwebhookconfigurations
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - autoscaling
  resources:
  - horizontalpodautoscalers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - keda.sh
  resources:
  - scaledobjects
  - scaledobjects/finalizers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - keda.sh
  resources:
  - scaledobjects/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.istio.io
  resources:
  - virtualservices
  - virtualservices/finalizers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.istio.io
  resources:
  - virtualservices/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - opentelemetry.io
  resources:
  - opentelemetrycollectors
  - opentelemetrycollectors/finalizers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - opentelemetry.io
  resources:
  - opentelemetrycollectors/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - serving.knative.dev
  resources:
  - services
  - services/finalizers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - serving.knative.dev
  resources:
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - serving.kserve.io
  resources:
  - clusterservingruntimes
  - clusterservingruntimes/finalizers
  - clusterstoragecontainers
  - inferencegraphs
  - inferencegraphs/finalizers
  - inferenceservices
  - inferenceservices/finalizers
  - servingruntimes
  - servingruntimes/finalizers
  - trainedmodels
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - serving.kserve.io
  resources:
  - clusterservingruntimes/status
  - inferencegraphs/status
  - inferenceservices/status
  - servingruntimes/status
  - trainedmodels/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - serving.kserve.io
  resources:
  - localmodelcaches
  verbs:
  - get
  - list
  - watch
