---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: llmisvc-manager-role
rules:
- apiGroups:
  - serving.kserve.io
  resources:
  - llminferenceservices
  - llminferenceservices/finalizers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - serving.kserve.io
  resources:
  - llminferenceservices/status
  verbs:
  - get
  - patch
  - update
