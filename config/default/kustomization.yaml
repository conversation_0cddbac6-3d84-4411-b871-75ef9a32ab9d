apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Adds namespace to all resources.
namespace: kserve

# Labels to add to all resources and selectors.
#commonLabels:
#  app.kubernetes.io/name: kserve
resources:
- ../crd
- ../configmap
- ../rbac
- ../manager
- ../webhook
- ../certmanager
- ../localmodels
- ../localmodelnodes

generatorOptions:
  disableNameSuffixHash: true



replacements:
- source:
    fieldPath: metadata.name
    kind: Service
    name: kserve-webhook-server-service
    version: v1
  targets:
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: MutatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: trainedmodel.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: inferencegraph.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: clusterservingruntime.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: servingruntime.serving.kserve.io
  - fieldPaths:
      - webhooks.*.clientConfig.service.name
    select:
      kind: ValidatingWebhookConfiguration
      name: localmodelcache.serving.kserve.io
  - fieldPaths:
    - spec.commonName
    - spec.dnsNames.0
    options:
      delimiter: '.'
      index: 0
    select:
      kind: Certificate
      name: serving-cert
      namespace: kserve
# Replace the namespace with the namespace of the controller manager.
- source:
    fieldPath: metadata.namespace
    kind: Deployment
    name: kserve-controller-manager
    version: v1
  targets:
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: MutatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: trainedmodel.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: inferencegraph.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: clusterservingruntime.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: servingruntime.serving.kserve.io
  - fieldPaths:
    - webhooks.*.clientConfig.service.namespace
    select:
      kind: ValidatingWebhookConfiguration
      name: localmodelcache.serving.kserve.io
  - fieldPaths:
    - spec.commonName
    - spec.dnsNames.0
    options:
      delimiter: '.'
      index: 1
    select:
      kind: Certificate
      name: serving-cert
      namespace: kserve
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: CustomResourceDefinition
      name: inferenceservices.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: MutatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: inferenceservice.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: trainedmodel.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: inferencegraph.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: clusterservingruntime.serving.kserve.io
  - fieldPaths:
    - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: servingruntime.serving.kserve.io
  - fieldPaths:
      - metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      delimiter: '/'
      index: 0
    select:
      kind: ValidatingWebhookConfiguration
      name: localmodelcache.serving.kserve.io

    # Protect the /metrics endpoint by putting it behind auth.
    # Only one of manager_auth_proxy_patch.yaml and
    # manager_prometheus_metrics_patch.yaml should be enabled.
    # If you want your controller-manager to expose the /metrics
    # endpoint w/o any authn/z, uncomment the following line and
    # comment manager_auth_proxy_patch.yaml.
    # Only one of manager_auth_proxy_patch.yaml and
    # manager_prometheus_metrics_patch.yaml should be enabled.
    #- manager_prometheus_metrics_patch.yaml
patches:
- path: manager_image_patch.yaml
- path: manager_auth_proxy_patch.yaml
- path: isvc_mutatingwebhook_cainjection_patch.yaml
- path: isvc_validatingwebhook_cainjection_patch.yaml
- path: inferencegraph_validatingwebhook_cainjection_patch.yaml
- path: trainedmodel_validatingwebhook_cainjection_patch.yaml
- path: clusterservingruntime_validatingwebhook_cainjection_patch.yaml
- path: servingruntime_validationwebhook_cainjection_patch.yaml
- path: localmodelcache_validatingwebhook_cainjection_patch.yaml
- path: manager_resources_patch.yaml
- path: cainjection_conversion_webhook.yaml
- path: localmodel_manager_image_patch.yaml
- path: localmodelnode_agent_image_patch.yaml
