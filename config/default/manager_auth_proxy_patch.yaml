# This patch inject a sidecar container which is a HTTP proxy for the controller manager,
# it performs RBAC authorization against the Kubernetes API using SubjectAccessReviews.
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kserve-controller-manager
  namespace: kserve
spec:
  template:
    spec:
      containers:
      - name: kube-rbac-proxy
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        image: quay.io/brancz/kube-rbac-proxy:v0.18.0
        args:
        - "--secure-listen-address=0.0.0.0:8443"
        - "--upstream=http://127.0.0.1:8080/"
        - "--logtostderr=true"
        - "--v=10"
        ports:
        - containerPort: 8443
          protocol: TCP
          name: https
      - name: manager
        args:
        # When changing args, also update manager.yaml because the following
        # list of args will fully override the arguments in manager.yaml.
        - "--metrics-addr=127.0.0.1:8080"
        - "--leader-elect"
