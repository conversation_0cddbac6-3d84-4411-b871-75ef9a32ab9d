apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kserve-localmodelnode-agent
  namespace: kserve
  labels:
    app.kubernetes.io/name: kserve-localmodelnode-agent
    control-plane: kserve-localmodelnode-agent
    controller-tools.k8s.io: "1.0"
spec:
  selector:
    matchLabels:
      control-plane: kserve-localmodelnode-agent
      controller-tools.k8s.io: "1.0"
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kserve-localmodelnode-agent
        control-plane: kserve-localmodelnode-agent
        controller-tools.k8s.io: "1.0"
      annotations:
        kubectl.kubernetes.io/default-container: manager
    spec:
      nodeSelector:
        kserve/localmodel: worker
      serviceAccountName: kserve-localmodelnode-agent
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      containers:
      - command:
        - /manager
        image: ko://github.com/kserve/kserve/cmd/localmodelnode
        imagePullPolicy: Always
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
        volumeMounts:
          - mountPath: /mnt/models
            name: models
            readOnly: false
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 200Mi
      volumes:
        - name: models
          hostPath:
            path: /models
            type: DirectoryOrCreate
      terminationGracePeriodSeconds: 10
