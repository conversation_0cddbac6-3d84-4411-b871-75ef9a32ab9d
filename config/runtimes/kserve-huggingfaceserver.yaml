apiVersion: serving.kserve.io/v1alpha1
kind: ClusterServingRuntime
metadata:
  name: kserve-huggingfaceserver
spec:
  annotations:
    prometheus.kserve.io/port: '8080'
    prometheus.kserve.io/path: "/metrics"
  supportedModelFormats:
    - name: huggingface
      version: "1"
      autoSelect: true
      priority: 1
  protocolVersions:
    - v2
    - v1
  containers:
    - name: kserve-container
      image: huggingfaceserver:replace
      args:
        - --model_name={{.Name}}
      env:
        - name: LMCACHE_USE_EXPERIMENTAL
          value: "True"
      securityContext:
        allowPrivilegeEscalation: false
        privileged: false
        runAsNonRoot: true
        capabilities:
          drop:
            - ALL
      resources:
        requests:
          cpu: "1"
          memory: 2Gi
        limits:
          cpu: "1"
          memory: 2Gi
      volumeMounts:
        - name: devshm
          mountPath: /dev/shm
  volumes:
    - name: devshm
      emptyDir:
        medium: Memory
  hostIPC: false
