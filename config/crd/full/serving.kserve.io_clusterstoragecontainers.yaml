---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.2
  name: clusterstoragecontainers.serving.kserve.io
spec:
  group: serving.kserve.io
  names:
    kind: ClusterStorageContainer
    listKind: ClusterStorageContainerList
    plural: clusterstoragecontainers
    singular: clusterstoragecontainer
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          disabled:
            type: boolean
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              container:
                properties:
                  args:
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  command:
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                        valueFrom:
                          properties:
                            configMapKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            fieldRef:
                              properties:
                                apiVersion:
                                  type: string
                                fieldPath:
                                  type: string
                              required:
                              - fieldPath
                              type: object
                              x-kubernetes-map-type: atomic
                            resourceFieldRef:
                              properties:
                                containerName:
                                  type: string
                                divisor:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                resource:
                                  type: string
                              required:
                              - resource
                              type: object
                              x-kubernetes-map-type: atomic
                            secretKeyRef:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  envFrom:
                    items:
                      properties:
                        configMapRef:
                          properties:
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          type: object
                          x-kubernetes-map-type: atomic
                        prefix:
                          type: string
                        secretRef:
                          properties:
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  image:
                    type: string
                  imagePullPolicy:
                    type: string
                  lifecycle:
                    properties:
                      postStart:
                        properties:
                          exec:
                            properties:
                              command:
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            type: object
                          httpGet:
                            properties:
                              host:
                                type: string
                              httpHeaders:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              path:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                              scheme:
                                type: string
                            required:
                            - port
                            type: object
                          sleep:
                            properties:
                              seconds:
                                format: int64
                                type: integer
                            required:
                            - seconds
                            type: object
                          tcpSocket:
                            properties:
                              host:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                            required:
                            - port
                            type: object
                        type: object
                      preStop:
                        properties:
                          exec:
                            properties:
                              command:
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            type: object
                          httpGet:
                            properties:
                              host:
                                type: string
                              httpHeaders:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              path:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                              scheme:
                                type: string
                            required:
                            - port
                            type: object
                          sleep:
                            properties:
                              seconds:
                                format: int64
                                type: integer
                            required:
                            - seconds
                            type: object
                          tcpSocket:
                            properties:
                              host:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                            required:
                            - port
                            type: object
                        type: object
                      stopSignal:
                        type: string
                    type: object
                  livenessProbe:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      failureThreshold:
                        format: int32
                        type: integer
                      grpc:
                        properties:
                          port:
                            format: int32
                            type: integer
                          service:
                            default: ""
                            type: string
                        required:
                        - port
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      initialDelaySeconds:
                        format: int32
                        type: integer
                      periodSeconds:
                        format: int32
                        type: integer
                      successThreshold:
                        format: int32
                        type: integer
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                      terminationGracePeriodSeconds:
                        format: int64
                        type: integer
                      timeoutSeconds:
                        format: int32
                        type: integer
                    type: object
                  name:
                    type: string
                  ports:
                    items:
                      properties:
                        containerPort:
                          format: int32
                          type: integer
                        hostIP:
                          type: string
                        hostPort:
                          format: int32
                          type: integer
                        name:
                          type: string
                        protocol:
                          default: TCP
                          type: string
                      required:
                      - containerPort
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - containerPort
                    - protocol
                    x-kubernetes-list-type: map
                  readinessProbe:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      failureThreshold:
                        format: int32
                        type: integer
                      grpc:
                        properties:
                          port:
                            format: int32
                            type: integer
                          service:
                            default: ""
                            type: string
                        required:
                        - port
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      initialDelaySeconds:
                        format: int32
                        type: integer
                      periodSeconds:
                        format: int32
                        type: integer
                      successThreshold:
                        format: int32
                        type: integer
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                      terminationGracePeriodSeconds:
                        format: int64
                        type: integer
                      timeoutSeconds:
                        format: int32
                        type: integer
                    type: object
                  resizePolicy:
                    items:
                      properties:
                        resourceName:
                          type: string
                        restartPolicy:
                          type: string
                      required:
                      - resourceName
                      - restartPolicy
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  resources:
                    properties:
                      claims:
                        items:
                          properties:
                            name:
                              type: string
                            request:
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        type: object
                    type: object
                  restartPolicy:
                    type: string
                  securityContext:
                    properties:
                      allowPrivilegeEscalation:
                        type: boolean
                      appArmorProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      capabilities:
                        properties:
                          add:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          drop:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      privileged:
                        type: boolean
                      procMount:
                        type: string
                      readOnlyRootFilesystem:
                        type: boolean
                      runAsGroup:
                        format: int64
                        type: integer
                      runAsNonRoot:
                        type: boolean
                      runAsUser:
                        format: int64
                        type: integer
                      seLinuxOptions:
                        properties:
                          level:
                            type: string
                          role:
                            type: string
                          type:
                            type: string
                          user:
                            type: string
                        type: object
                      seccompProfile:
                        properties:
                          localhostProfile:
                            type: string
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      windowsOptions:
                        properties:
                          gmsaCredentialSpec:
                            type: string
                          gmsaCredentialSpecName:
                            type: string
                          hostProcess:
                            type: boolean
                          runAsUserName:
                            type: string
                        type: object
                    type: object
                  startupProbe:
                    properties:
                      exec:
                        properties:
                          command:
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                        type: object
                      failureThreshold:
                        format: int32
                        type: integer
                      grpc:
                        properties:
                          port:
                            format: int32
                            type: integer
                          service:
                            default: ""
                            type: string
                        required:
                        - port
                        type: object
                      httpGet:
                        properties:
                          host:
                            type: string
                          httpHeaders:
                            items:
                              properties:
                                name:
                                  type: string
                                value:
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            type: array
                            x-kubernetes-list-type: atomic
                          path:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          scheme:
                            type: string
                        required:
                        - port
                        type: object
                      initialDelaySeconds:
                        format: int32
                        type: integer
                      periodSeconds:
                        format: int32
                        type: integer
                      successThreshold:
                        format: int32
                        type: integer
                      tcpSocket:
                        properties:
                          host:
                            type: string
                          port:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - port
                        type: object
                      terminationGracePeriodSeconds:
                        format: int64
                        type: integer
                      timeoutSeconds:
                        format: int32
                        type: integer
                    type: object
                  stdin:
                    type: boolean
                  stdinOnce:
                    type: boolean
                  terminationMessagePath:
                    type: string
                  terminationMessagePolicy:
                    type: string
                  tty:
                    type: boolean
                  volumeDevices:
                    items:
                      properties:
                        devicePath:
                          type: string
                        name:
                          type: string
                      required:
                      - devicePath
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - devicePath
                    x-kubernetes-list-type: map
                  volumeMounts:
                    items:
                      properties:
                        mountPath:
                          type: string
                        mountPropagation:
                          type: string
                        name:
                          type: string
                        readOnly:
                          type: boolean
                        recursiveReadOnly:
                          type: string
                        subPath:
                          type: string
                        subPathExpr:
                          type: string
                      required:
                      - mountPath
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - mountPath
                    x-kubernetes-list-type: map
                  workingDir:
                    type: string
                required:
                - name
                type: object
              supportedUriFormats:
                items:
                  properties:
                    prefix:
                      type: string
                    regex:
                      type: string
                  type: object
                type: array
              workloadType:
                default: initContainer
                type: string
            required:
            - container
            - supportedUriFormats
            type: object
        type: object
    served: true
    storage: true
