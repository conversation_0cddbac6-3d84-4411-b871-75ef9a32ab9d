apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.2
  name: llminferenceserviceconfigs.serving.kserve.io
spec:
  group: serving.kserve.io
  names:
    kind: LLMInferenceServiceConfig
    listKind: LLMInferenceServiceConfigList
    plural: llminferenceserviceconfigs
    singular: llminferenceserviceconfig
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
