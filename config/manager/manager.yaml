apiVersion: apps/v1
kind: Deployment
metadata:
  name: kserve-controller-manager
  namespace: kserve
  labels:
    app.kubernetes.io/name: kserve-controller-manager
    control-plane: kserve-controller-manager
    controller-tools.k8s.io: "1.0"
spec:
  selector:
    matchLabels:
      control-plane: kserve-controller-manager
      controller-tools.k8s.io: "1.0"
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kserve-controller-manager
        control-plane: kserve-controller-manager
        controller-tools.k8s.io: "1.0"
      annotations:
        kubectl.kubernetes.io/default-container: manager
    spec:
      serviceAccountName: kserve-controller-manager
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      containers:
      - command:
        - /manager
        args:
        # When changing arguments, make sure to review the args in manager_auth_proxy_patch.yaml
        # and update as needed.
        - "--leader-elect"
        image: ko://github.com/kserve/kserve/cmd/manager
        imagePullPolicy: Always
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: SECRET_NAME
            value: kserve-webhook-server-cert
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 30
          httpGet:
            path: /healthz
            port: 8081
          timeoutSeconds: 5
        readinessProbe:
          initialDelaySeconds: 30
          failureThreshold: 5
          periodSeconds: 5
          httpGet:
            path: /readyz
            port: 8081
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 200Mi
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      terminationGracePeriodSeconds: 10
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: kserve-webhook-server-cert
---
apiVersion: v1
kind: Secret
metadata:
  name: kserve-webhook-server-secret
  namespace: kserve
