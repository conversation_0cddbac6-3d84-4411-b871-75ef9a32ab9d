## CNCF Community Code of Conduct v1.3

Other languages available:
- [Arabic/العربية](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/ar.md)
- [Bengali/বাংলা](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/bn.md)
- [Bulgarian/Български](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/bg.md)
- [Chinese/中文](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/zh.md)
- [Czech/Česky](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/cs.md)
- [Farsi/فارسی](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/fa.md)
- [French/Français](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/fr.md)
- [German/Deutsch](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/de.md)
- [Hebrew/עברית](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/he.md)
- [Hindi/हिन्दी](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/hi.md)
- [Hungarian/Magyar](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/hu.md)
- [Indonesian/Bahasa Indonesia](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/id.md)
- [Italian/Italiano](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/it.md)
- [Japanese/日本語](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/ja.md)
- [Korean/한국어](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/ko.md)
- [Polish/Polski](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/pl.md)
- [Portuguese/Português](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/pt.md)
- [Russian/Русский](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/ru.md)
- [Spanish/Español](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/es.md)
- [Turkish/Türkçe](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/tr.md)
- [Ukrainian/Українська](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/uk.md)
- [Vietnamese/Tiếng Việt](https://github.com/cncf/foundation/blob/main/code-of-conduct-languages/vi.md)

### Community Code of Conduct

As contributors, maintainers, and participants in the CNCF community, and in the interest of fostering
an open and welcoming community, we pledge to respect all people who participate or contribute
through reporting issues, posting feature requests, updating documentation,
submitting pull requests or patches, attending conferences or events, or engaging in other community or project activities.

We are committed to making participation in the CNCF community a harassment-free experience for everyone, regardless of age, body size, caste, disability, ethnicity, level of experience, family status, gender, gender identity and expression, marital status, military or veteran status, nationality, personal appearance, race, religion, sexual orientation, socioeconomic status, tribe, or any other dimension of diversity.

## Scope

This code of conduct applies:
* within project and community spaces,
* in other spaces when an individual CNCF community participant's words or actions are directed at or are about a CNCF project, the CNCF community, or another CNCF community participant in the context of a CNCF activity.

### CNCF Events

CNCF events that are produced by the Linux Foundation with professional events staff are governed by the Linux Foundation [Events Code of Conduct](https://events.linuxfoundation.org/code-of-conduct/) available on the event page. This is designed to be used in conjunction with the CNCF Code of Conduct.

## Our Standards

The CNCF Community is open, inclusive and respectful. Every member of our community has the right to have their identity respected.

Examples of behavior that contributes to a positive environment include but are not limited to:

* Demonstrating empathy and kindness toward other people
* Being respectful of differing opinions, viewpoints, and experiences
* Giving and gracefully accepting constructive feedback
* Accepting responsibility and apologizing to those affected by our mistakes,
  and learning from the experience
* Focusing on what is best not just for us as individuals, but for the
  overall community
* Using welcoming and inclusive language


Examples of unacceptable behavior include but are not limited to:

* The use of sexualized language or imagery
* Trolling, insulting or derogatory comments, and personal or political attacks
* Public or private harassment in any form
* Publishing others' private information, such as a physical or email
  address, without their explicit permission
* Violence, threatening violence, or encouraging others to engage in violent behavior
* Stalking or following someone without their consent
* Unwelcome physical contact
* Unwelcome sexual or romantic attention or advances
* Using CNCF projects or community spaces for political campaigning or promotion of political causes 
  that are unrelated to the advancement of cloud native technology. To clarify, this policy does not restrict individuals' personal attire, including attire that expresses personal beliefs or aspects of identity.
* Other conduct which could reasonably be considered inappropriate in a
  professional setting

The following behaviors are also prohibited:
* Providing knowingly false or misleading information in connection with a Code of Conduct investigation or otherwise intentionally tampering with an investigation.
* Retaliating against a person because they reported an incident or provided information about an incident as a witness.

Project maintainers have the right and responsibility to remove, edit, or reject comments, commits, code, wiki edits, issues, and other contributions that are not aligned to this Code of Conduct.
By adopting this Code of Conduct, project maintainers commit themselves to fairly and consistently applying these principles to every aspect
of managing a CNCF project.
Project maintainers who do not follow or enforce the Code of Conduct may be temporarily or permanently removed from the project team.

## Reporting

For incidents occurring in the Kubernetes community, contact the [Kubernetes Code of Conduct Committee](https://git.k8s.io/community/committee-code-of-conduct) via <<EMAIL>>. You can expect a response within three business days.

For other projects, or for incidents that are project-agnostic or impact multiple CNCF projects, please contact the [CNCF Code of Conduct Committee](https://www.cncf.io/conduct/committee/) via <<EMAIL>>.  Alternatively, you can contact any of the individual members of the [CNCF Code of Conduct Committee](https://www.cncf.io/conduct/committee/) to submit your report. For more detailed instructions on how to submit a report, including how to submit a report anonymously, please see our [Incident Resolution Procedures](https://github.com/cncf/foundation/blob/main/code-of-conduct/coc-incident-resolution-procedures.md). You can expect a response within three business days.

For incidents occurring at CNCF event that is produced by the Linux Foundation, please contact <<EMAIL>>.

## Frequently asked questions
For more information about this Code of Conduct, please see the [CNCF Code of Conduct Frequently Asked Questions](https://www.cncf.io/conduct/faq/).

## Enforcement

Upon review and investigation of a reported incident, the CoC response team that has jurisdiction will determine what action is appropriate based on this Code of Conduct and its related documentation.

For information about which Code of Conduct incidents are handled by project leadership, which incidents are handled by the CNCF Code of Conduct Committee, and which incidents are handled by the Linux Foundation (including its events team), see our [Jurisdiction Policy](https://github.com/cncf/foundation/blob/main/code-of-conduct/coc-committee-jurisdiction-policy.md).

## Amendments

Consistent with the CNCF Charter, any substantive changes to this Code of Conduct must be approved by the Technical Oversight Committee.

## Acknowledgements

This Code of Conduct is adapted from the Contributor Covenant
(http://contributor-covenant.org), version 2.0 available at
http://contributor-covenant.org/version/2/0/code_of_conduct/
