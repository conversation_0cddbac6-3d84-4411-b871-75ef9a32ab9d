# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1alpha1InfereceGraphRouterTimeouts(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'server_idle': 'int',
        'server_read': 'int',
        'server_write': 'int',
        'service_client': 'int'
    }

    attribute_map = {
        'server_idle': 'serverIdle',
        'server_read': 'serverRead',
        'server_write': 'serverWrite',
        'service_client': 'serviceClient'
    }

    def __init__(self, server_idle=None, server_read=None, server_write=None, service_client=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha1InfereceGraphRouterTimeouts - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._server_idle = None
        self._server_read = None
        self._server_write = None
        self._service_client = None
        self.discriminator = None

        if server_idle is not None:
            self.server_idle = server_idle
        if server_read is not None:
            self.server_read = server_read
        if server_write is not None:
            self.server_write = server_write
        if service_client is not None:
            self.service_client = service_client

    @property
    def server_idle(self):
        """Gets the server_idle of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501

        ServerIdle specifies the maximum amount of time in seconds to wait for the next request when keep-alives are enabled.  # noqa: E501

        :return: The server_idle of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :rtype: int
        """
        return self._server_idle

    @server_idle.setter
    def server_idle(self, server_idle):
        """Sets the server_idle of this V1alpha1InfereceGraphRouterTimeouts.

        ServerIdle specifies the maximum amount of time in seconds to wait for the next request when keep-alives are enabled.  # noqa: E501

        :param server_idle: The server_idle of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :type: int
        """

        self._server_idle = server_idle

    @property
    def server_read(self):
        """Gets the server_read of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501

        ServerRead specifies the number of seconds to wait before timing out a request read by the server.  # noqa: E501

        :return: The server_read of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :rtype: int
        """
        return self._server_read

    @server_read.setter
    def server_read(self, server_read):
        """Sets the server_read of this V1alpha1InfereceGraphRouterTimeouts.

        ServerRead specifies the number of seconds to wait before timing out a request read by the server.  # noqa: E501

        :param server_read: The server_read of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :type: int
        """

        self._server_read = server_read

    @property
    def server_write(self):
        """Gets the server_write of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501

        ServerWrite specifies the maximum duration in seconds before timing out writes of the response.  # noqa: E501

        :return: The server_write of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :rtype: int
        """
        return self._server_write

    @server_write.setter
    def server_write(self, server_write):
        """Sets the server_write of this V1alpha1InfereceGraphRouterTimeouts.

        ServerWrite specifies the maximum duration in seconds before timing out writes of the response.  # noqa: E501

        :param server_write: The server_write of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :type: int
        """

        self._server_write = server_write

    @property
    def service_client(self):
        """Gets the service_client of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501

        ServiceClient specifies a time limit in seconds for requests made to the graph components by HTTP client.  # noqa: E501

        :return: The service_client of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :rtype: int
        """
        return self._service_client

    @service_client.setter
    def service_client(self, service_client):
        """Sets the service_client of this V1alpha1InfereceGraphRouterTimeouts.

        ServiceClient specifies a time limit in seconds for requests made to the graph components by HTTP client.  # noqa: E501

        :param service_client: The service_client of this V1alpha1InfereceGraphRouterTimeouts.  # noqa: E501
        :type: int
        """

        self._service_client = service_client

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha1InfereceGraphRouterTimeouts):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha1InfereceGraphRouterTimeouts):
            return True

        return self.to_dict() != other.to_dict()
