# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1ResourceConfig(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'cpu_limit': 'str',
        'cpu_request': 'str',
        'memory_limit': 'str',
        'memory_request': 'str'
    }

    attribute_map = {
        'cpu_limit': 'cpuLimit',
        'cpu_request': 'cpuRequest',
        'memory_limit': 'memoryLimit',
        'memory_request': 'memoryRequest'
    }

    def __init__(self, cpu_limit=None, cpu_request=None, memory_limit=None, memory_request=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1ResourceConfig - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._cpu_limit = None
        self._cpu_request = None
        self._memory_limit = None
        self._memory_request = None
        self.discriminator = None

        if cpu_limit is not None:
            self.cpu_limit = cpu_limit
        if cpu_request is not None:
            self.cpu_request = cpu_request
        if memory_limit is not None:
            self.memory_limit = memory_limit
        if memory_request is not None:
            self.memory_request = memory_request

    @property
    def cpu_limit(self):
        """Gets the cpu_limit of this V1beta1ResourceConfig.  # noqa: E501


        :return: The cpu_limit of this V1beta1ResourceConfig.  # noqa: E501
        :rtype: str
        """
        return self._cpu_limit

    @cpu_limit.setter
    def cpu_limit(self, cpu_limit):
        """Sets the cpu_limit of this V1beta1ResourceConfig.


        :param cpu_limit: The cpu_limit of this V1beta1ResourceConfig.  # noqa: E501
        :type: str
        """

        self._cpu_limit = cpu_limit

    @property
    def cpu_request(self):
        """Gets the cpu_request of this V1beta1ResourceConfig.  # noqa: E501


        :return: The cpu_request of this V1beta1ResourceConfig.  # noqa: E501
        :rtype: str
        """
        return self._cpu_request

    @cpu_request.setter
    def cpu_request(self, cpu_request):
        """Sets the cpu_request of this V1beta1ResourceConfig.


        :param cpu_request: The cpu_request of this V1beta1ResourceConfig.  # noqa: E501
        :type: str
        """

        self._cpu_request = cpu_request

    @property
    def memory_limit(self):
        """Gets the memory_limit of this V1beta1ResourceConfig.  # noqa: E501


        :return: The memory_limit of this V1beta1ResourceConfig.  # noqa: E501
        :rtype: str
        """
        return self._memory_limit

    @memory_limit.setter
    def memory_limit(self, memory_limit):
        """Sets the memory_limit of this V1beta1ResourceConfig.


        :param memory_limit: The memory_limit of this V1beta1ResourceConfig.  # noqa: E501
        :type: str
        """

        self._memory_limit = memory_limit

    @property
    def memory_request(self):
        """Gets the memory_request of this V1beta1ResourceConfig.  # noqa: E501


        :return: The memory_request of this V1beta1ResourceConfig.  # noqa: E501
        :rtype: str
        """
        return self._memory_request

    @memory_request.setter
    def memory_request(self, memory_request):
        """Sets the memory_request of this V1beta1ResourceConfig.


        :param memory_request: The memory_request of this V1beta1ResourceConfig.  # noqa: E501
        :type: str
        """

        self._memory_request = memory_request

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1ResourceConfig):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1ResourceConfig):
            return True

        return self.to_dict() != other.to_dict()
