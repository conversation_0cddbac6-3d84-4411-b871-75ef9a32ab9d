# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1MetricsConfig(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'metrics_backend': 'str',
        'server_address': 'str'
    }

    attribute_map = {
        'metrics_backend': 'metricsBackend',
        'server_address': 'serverAddress'
    }

    def __init__(self, metrics_backend=None, server_address=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1MetricsConfig - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._metrics_backend = None
        self._server_address = None
        self.discriminator = None

        if metrics_backend is not None:
            self.metrics_backend = metrics_backend
        if server_address is not None:
            self.server_address = server_address

    @property
    def metrics_backend(self):
        """Gets the metrics_backend of this V1beta1MetricsConfig.  # noqa: E501


        :return: The metrics_backend of this V1beta1MetricsConfig.  # noqa: E501
        :rtype: str
        """
        return self._metrics_backend

    @metrics_backend.setter
    def metrics_backend(self, metrics_backend):
        """Sets the metrics_backend of this V1beta1MetricsConfig.


        :param metrics_backend: The metrics_backend of this V1beta1MetricsConfig.  # noqa: E501
        :type: str
        """

        self._metrics_backend = metrics_backend

    @property
    def server_address(self):
        """Gets the server_address of this V1beta1MetricsConfig.  # noqa: E501


        :return: The server_address of this V1beta1MetricsConfig.  # noqa: E501
        :rtype: str
        """
        return self._server_address

    @server_address.setter
    def server_address(self, server_address):
        """Sets the server_address of this V1beta1MetricsConfig.


        :param server_address: The server_address of this V1beta1MetricsConfig.  # noqa: E501
        :type: str
        """

        self._server_address = server_address

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1MetricsConfig):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1MetricsConfig):
            return True

        return self.to_dict() != other.to_dict()
