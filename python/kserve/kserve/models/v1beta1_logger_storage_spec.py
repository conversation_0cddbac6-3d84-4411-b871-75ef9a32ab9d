# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1LoggerStorageSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'key': 'str',
        'parameters': 'dict(str, str)',
        'path': 'str',
        'service_account_name': 'str'
    }

    attribute_map = {
        'key': 'key',
        'parameters': 'parameters',
        'path': 'path',
        'service_account_name': 'serviceAccountName'
    }

    def __init__(self, key=None, parameters=None, path=None, service_account_name=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1LoggerStorageSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._key = None
        self._parameters = None
        self._path = None
        self._service_account_name = None
        self.discriminator = None

        if key is not None:
            self.key = key
        if parameters is not None:
            self.parameters = parameters
        if path is not None:
            self.path = path
        if service_account_name is not None:
            self.service_account_name = service_account_name

    @property
    def key(self):
        """Gets the key of this V1beta1LoggerStorageSpec.  # noqa: E501

        The Storage Key in the secret for this object.  # noqa: E501

        :return: The key of this V1beta1LoggerStorageSpec.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this V1beta1LoggerStorageSpec.

        The Storage Key in the secret for this object.  # noqa: E501

        :param key: The key of this V1beta1LoggerStorageSpec.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def parameters(self):
        """Gets the parameters of this V1beta1LoggerStorageSpec.  # noqa: E501

        Parameters to override the default storage credentials and config.  # noqa: E501

        :return: The parameters of this V1beta1LoggerStorageSpec.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._parameters

    @parameters.setter
    def parameters(self, parameters):
        """Sets the parameters of this V1beta1LoggerStorageSpec.

        Parameters to override the default storage credentials and config.  # noqa: E501

        :param parameters: The parameters of this V1beta1LoggerStorageSpec.  # noqa: E501
        :type: dict(str, str)
        """

        self._parameters = parameters

    @property
    def path(self):
        """Gets the path of this V1beta1LoggerStorageSpec.  # noqa: E501

        The path to the object in the storage. Note that this path is relative to the storage URI.  # noqa: E501

        :return: The path of this V1beta1LoggerStorageSpec.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this V1beta1LoggerStorageSpec.

        The path to the object in the storage. Note that this path is relative to the storage URI.  # noqa: E501

        :param path: The path of this V1beta1LoggerStorageSpec.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def service_account_name(self):
        """Gets the service_account_name of this V1beta1LoggerStorageSpec.  # noqa: E501


        :return: The service_account_name of this V1beta1LoggerStorageSpec.  # noqa: E501
        :rtype: str
        """
        return self._service_account_name

    @service_account_name.setter
    def service_account_name(self, service_account_name):
        """Sets the service_account_name of this V1beta1LoggerStorageSpec.


        :param service_account_name: The service_account_name of this V1beta1LoggerStorageSpec.  # noqa: E501
        :type: str
        """

        self._service_account_name = service_account_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1LoggerStorageSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1LoggerStorageSpec):
            return True

        return self.to_dict() != other.to_dict()
