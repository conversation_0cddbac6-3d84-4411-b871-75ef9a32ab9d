# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1ExtMetricAuthentication(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'auth_modes': 'str',
        'authentication_ref': 'V1beta1AuthenticationRef'
    }

    attribute_map = {
        'auth_modes': 'authModes',
        'authentication_ref': 'authenticationRef'
    }

    def __init__(self, auth_modes=None, authentication_ref=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1ExtMetricAuthentication - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._auth_modes = None
        self._authentication_ref = None
        self.discriminator = None

        if auth_modes is not None:
            self.auth_modes = auth_modes
        self.authentication_ref = authentication_ref

    @property
    def auth_modes(self):
        """Gets the auth_modes of this V1beta1ExtMetricAuthentication.  # noqa: E501

        authModes defines the authentication modes for the metrics backend possible values are bearer, basic, tls. for more information see: https://keda.sh/docs/2.17/scalers/prometheus/#authentication-parameters  # noqa: E501

        :return: The auth_modes of this V1beta1ExtMetricAuthentication.  # noqa: E501
        :rtype: str
        """
        return self._auth_modes

    @auth_modes.setter
    def auth_modes(self, auth_modes):
        """Sets the auth_modes of this V1beta1ExtMetricAuthentication.

        authModes defines the authentication modes for the metrics backend possible values are bearer, basic, tls. for more information see: https://keda.sh/docs/2.17/scalers/prometheus/#authentication-parameters  # noqa: E501

        :param auth_modes: The auth_modes of this V1beta1ExtMetricAuthentication.  # noqa: E501
        :type: str
        """

        self._auth_modes = auth_modes

    @property
    def authentication_ref(self):
        """Gets the authentication_ref of this V1beta1ExtMetricAuthentication.  # noqa: E501


        :return: The authentication_ref of this V1beta1ExtMetricAuthentication.  # noqa: E501
        :rtype: V1beta1AuthenticationRef
        """
        return self._authentication_ref

    @authentication_ref.setter
    def authentication_ref(self, authentication_ref):
        """Sets the authentication_ref of this V1beta1ExtMetricAuthentication.


        :param authentication_ref: The authentication_ref of this V1beta1ExtMetricAuthentication.  # noqa: E501
        :type: V1beta1AuthenticationRef
        """
        if self.local_vars_configuration.client_side_validation and authentication_ref is None:  # noqa: E501
            raise ValueError("Invalid value for `authentication_ref`, must not be `None`")  # noqa: E501

        self._authentication_ref = authentication_ref

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1ExtMetricAuthentication):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1ExtMetricAuthentication):
            return True

        return self.to_dict() != other.to_dict()
