# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1MultiNodeConfig(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'custom_gpu_resource_type_list': 'list[str]'
    }

    attribute_map = {
        'custom_gpu_resource_type_list': 'customGPUResourceTypeList'
    }

    def __init__(self, custom_gpu_resource_type_list=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1MultiNodeConfig - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._custom_gpu_resource_type_list = None
        self.discriminator = None

        if custom_gpu_resource_type_list is not None:
            self.custom_gpu_resource_type_list = custom_gpu_resource_type_list

    @property
    def custom_gpu_resource_type_list(self):
        """Gets the custom_gpu_resource_type_list of this V1beta1MultiNodeConfig.  # noqa: E501

        CustomGPUResourceTypeList is a list of custom GPU resource types that are allowed to be used in the ServingRuntime and inferenceService  # noqa: E501

        :return: The custom_gpu_resource_type_list of this V1beta1MultiNodeConfig.  # noqa: E501
        :rtype: list[str]
        """
        return self._custom_gpu_resource_type_list

    @custom_gpu_resource_type_list.setter
    def custom_gpu_resource_type_list(self, custom_gpu_resource_type_list):
        """Sets the custom_gpu_resource_type_list of this V1beta1MultiNodeConfig.

        CustomGPUResourceTypeList is a list of custom GPU resource types that are allowed to be used in the ServingRuntime and inferenceService  # noqa: E501

        :param custom_gpu_resource_type_list: The custom_gpu_resource_type_list of this V1beta1MultiNodeConfig.  # noqa: E501
        :type: list[str]
        """

        self._custom_gpu_resource_type_list = custom_gpu_resource_type_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1MultiNodeConfig):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1MultiNodeConfig):
            return True

        return self.to_dict() != other.to_dict()
