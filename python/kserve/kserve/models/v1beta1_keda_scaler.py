# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1KedaScaler(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'idle_replica_count': 'int',
        'max_replica_count': 'int',
        'min_replica_count': 'int',
        'triggers': 'list[GithubComKedacoreKedaV2ApisKedaV1alpha1ScaleTriggers]'
    }

    attribute_map = {
        'idle_replica_count': 'idleReplicaCount',
        'max_replica_count': 'maxReplicaCount',
        'min_replica_count': 'minReplicaCount',
        'triggers': 'triggers'
    }

    def __init__(self, idle_replica_count=None, max_replica_count=None, min_replica_count=None, triggers=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1KedaScaler - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._idle_replica_count = None
        self._max_replica_count = None
        self._min_replica_count = None
        self._triggers = None
        self.discriminator = None

        if idle_replica_count is not None:
            self.idle_replica_count = idle_replica_count
        if max_replica_count is not None:
            self.max_replica_count = max_replica_count
        if min_replica_count is not None:
            self.min_replica_count = min_replica_count
        if triggers is not None:
            self.triggers = triggers

    @property
    def idle_replica_count(self):
        """Gets the idle_replica_count of this V1beta1KedaScaler.  # noqa: E501

        Number of idle replicas, Default: ignored, must be less than minReplicaCount  # noqa: E501

        :return: The idle_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :rtype: int
        """
        return self._idle_replica_count

    @idle_replica_count.setter
    def idle_replica_count(self, idle_replica_count):
        """Sets the idle_replica_count of this V1beta1KedaScaler.

        Number of idle replicas, Default: ignored, must be less than minReplicaCount  # noqa: E501

        :param idle_replica_count: The idle_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :type: int
        """

        self._idle_replica_count = idle_replica_count

    @property
    def max_replica_count(self):
        """Gets the max_replica_count of this V1beta1KedaScaler.  # noqa: E501

        Maximum number of replicas for autoscaling.  # noqa: E501

        :return: The max_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :rtype: int
        """
        return self._max_replica_count

    @max_replica_count.setter
    def max_replica_count(self, max_replica_count):
        """Sets the max_replica_count of this V1beta1KedaScaler.

        Maximum number of replicas for autoscaling.  # noqa: E501

        :param max_replica_count: The max_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :type: int
        """

        self._max_replica_count = max_replica_count

    @property
    def min_replica_count(self):
        """Gets the min_replica_count of this V1beta1KedaScaler.  # noqa: E501

        Minimum number of replicas, default: 0  # noqa: E501

        :return: The min_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :rtype: int
        """
        return self._min_replica_count

    @min_replica_count.setter
    def min_replica_count(self, min_replica_count):
        """Sets the min_replica_count of this V1beta1KedaScaler.

        Minimum number of replicas, default: 0  # noqa: E501

        :param min_replica_count: The min_replica_count of this V1beta1KedaScaler.  # noqa: E501
        :type: int
        """

        self._min_replica_count = min_replica_count

    @property
    def triggers(self):
        """Gets the triggers of this V1beta1KedaScaler.  # noqa: E501


        :return: The triggers of this V1beta1KedaScaler.  # noqa: E501
        :rtype: list[GithubComKedacoreKedaV2ApisKedaV1alpha1ScaleTriggers]
        """
        return self._triggers

    @triggers.setter
    def triggers(self, triggers):
        """Sets the triggers of this V1beta1KedaScaler.


        :param triggers: The triggers of this V1beta1KedaScaler.  # noqa: E501
        :type: list[GithubComKedacoreKedaV2ApisKedaV1alpha1ScaleTriggers]
        """

        self._triggers = triggers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1KedaScaler):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1KedaScaler):
            return True

        return self.to_dict() != other.to_dict()
