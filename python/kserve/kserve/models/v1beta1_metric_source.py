# Copyright 2023 The KServe Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# coding: utf-8

"""
    KServe

    Python SDK for KServe  # noqa: E501

    The version of the OpenAPI document: v0.1
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kserve.configuration import Configuration


class V1beta1MetricSource(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'backend': 'str',
        'namespace': 'str',
        'query': 'str',
        'server_address': 'str'
    }

    attribute_map = {
        'backend': 'backend',
        'namespace': 'namespace',
        'query': 'query',
        'server_address': 'serverAddress'
    }

    def __init__(self, backend=None, namespace=None, query=None, server_address=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1MetricSource - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._backend = None
        self._namespace = None
        self._query = None
        self._server_address = None
        self.discriminator = None

        if backend is not None:
            self.backend = backend
        if namespace is not None:
            self.namespace = namespace
        if query is not None:
            self.query = query
        if server_address is not None:
            self.server_address = server_address

    @property
    def backend(self):
        """Gets the backend of this V1beta1MetricSource.  # noqa: E501

        MetricsBackend defines the scaling metric type watched by autoscaler possible values are prometheus, graphite.  # noqa: E501

        :return: The backend of this V1beta1MetricSource.  # noqa: E501
        :rtype: str
        """
        return self._backend

    @backend.setter
    def backend(self, backend):
        """Sets the backend of this V1beta1MetricSource.

        MetricsBackend defines the scaling metric type watched by autoscaler possible values are prometheus, graphite.  # noqa: E501

        :param backend: The backend of this V1beta1MetricSource.  # noqa: E501
        :type: str
        """

        self._backend = backend

    @property
    def namespace(self):
        """Gets the namespace of this V1beta1MetricSource.  # noqa: E501

        For namespaced query  # noqa: E501

        :return: The namespace of this V1beta1MetricSource.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this V1beta1MetricSource.

        For namespaced query  # noqa: E501

        :param namespace: The namespace of this V1beta1MetricSource.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def query(self):
        """Gets the query of this V1beta1MetricSource.  # noqa: E501

        Query to run to get metrics from MetricsBackend  # noqa: E501

        :return: The query of this V1beta1MetricSource.  # noqa: E501
        :rtype: str
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this V1beta1MetricSource.

        Query to run to get metrics from MetricsBackend  # noqa: E501

        :param query: The query of this V1beta1MetricSource.  # noqa: E501
        :type: str
        """

        self._query = query

    @property
    def server_address(self):
        """Gets the server_address of this V1beta1MetricSource.  # noqa: E501

        Address of MetricsBackend server.  # noqa: E501

        :return: The server_address of this V1beta1MetricSource.  # noqa: E501
        :rtype: str
        """
        return self._server_address

    @server_address.setter
    def server_address(self, server_address):
        """Sets the server_address of this V1beta1MetricSource.

        Address of MetricsBackend server.  # noqa: E501

        :param server_address: The server_address of this V1beta1MetricSource.  # noqa: E501
        :type: str
        """

        self._server_address = server_address

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1MetricSource):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1MetricSource):
            return True

        return self.to_dict() != other.to_dict()
