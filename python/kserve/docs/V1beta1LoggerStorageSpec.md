# V1beta1LoggerStorageSpec

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**key** | **str** | The Storage Key in the secret for this object. | [optional] 
**parameters** | **dict(str, str)** | Parameters to override the default storage credentials and config. | [optional] 
**path** | **str** | The path to the object in the storage. Note that this path is relative to the storage URI. | [optional] 
**service_account_name** | **str** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


