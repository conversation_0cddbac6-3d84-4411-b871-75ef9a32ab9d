# V1beta1ExtMetricAuthentication

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**auth_modes** | **str** | authModes defines the authentication modes for the metrics backend possible values are bearer, basic, tls. for more information see: https://keda.sh/docs/2.17/scalers/prometheus/#authentication-parameters | [optional] 
**authentication_ref** | [**V1beta1AuthenticationRef**](V1beta1AuthenticationRef.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


