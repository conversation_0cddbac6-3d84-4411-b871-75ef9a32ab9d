# V1alpha1InfereceGraphRouterTimeouts

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**server_idle** | **int** | ServerIdle specifies the maximum amount of time in seconds to wait for the next request when keep-alives are enabled. | [optional] 
**server_read** | **int** | ServerRead specifies the number of seconds to wait before timing out a request read by the server. | [optional] 
**server_write** | **int** | ServerWrite specifies the maximum duration in seconds before timing out writes of the response. | [optional] 
**service_client** | **int** | ServiceClient specifies a time limit in seconds for requests made to the graph components by HTTP client. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


