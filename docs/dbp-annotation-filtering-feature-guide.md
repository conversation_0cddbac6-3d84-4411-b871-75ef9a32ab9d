# KServe RawDeployment 模式 dbp 注解过滤功能说明

## 📋 功能概述

本功能为 KServe RawDeployment 模式添加了智能注解过滤机制，通过环境变量配置，可以防止特定注解（如 `dbp-*` 前缀注解）的变更触发不必要的 Pod 重启，从而提升系统稳定性和性能。

## 🎯 解决的问题

在 KServe RawDeployment 模式下，当 InferenceService 的注解发生变更时，会导致 Deployment 的 Pod 模板更新，进而触发 Pod 重启。对于某些元数据类注解（如 `dbp-description`、`dbp-name` 等），这种重启是不必要的，会影响服务的可用性。

## ✨ 核心特性

### 1. 智能注解过滤
- **条件过滤**：只有当变更仅涉及被排除的注解时，才会过滤 Pod 模板中的这些注解
- **一致性保证**：如果有其他非注解变更，会正常更新所有内容，确保 Deployment 和 Pod 注解的一致性
- **双层更新**：Deployment 级别的注解始终会更新，只有 Pod 模板中的注解会被智能过滤

### 2. 灵活配置方式
- **环境变量配置**：通过环境变量进行配置，无需修改代码或重启服务
- **前缀匹配**：支持按注解前缀过滤（如 `dbp-`）
- **完全匹配**：支持按完整注解名称过滤
- **多值支持**：支持配置多个前缀或注解名称，用逗号分隔

### 3. 最小侵入性设计
- **零破坏性**：不修改任何现有结构体或函数签名
- **向后兼容**：不影响现有功能，默认行为保持不变
- **可扩展性**：支持未来添加更多配置选项

## 🔧 配置方式

### 环境变量

| 环境变量名 | 描述 | 默认值 | 示例 |
|-----------|------|--------|------|
| `POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES` | 需要过滤的注解前缀列表，用逗号分隔 | `"dbp-"` | `"dbp-,meta-,desc-"` |
| `POD_RESTART_EXCLUDED_ANNOTATIONS` | 需要过滤的完整注解名称列表，用逗号分隔 | 空 | `"exact-annotation,another-exact"` |

### 配置示例

```bash
# 基础配置：只过滤 dbp- 前缀的注解
POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES="dbp-"

# 高级配置：过滤多个前缀和特定注解
POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES="dbp-,meta-,desc-"
POD_RESTART_EXCLUDED_ANNOTATIONS="custom-annotation,special-tag"
```

### Kubernetes 部署配置

```yaml
# 在 Kubernetes Deployment 中配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kserve-controller-manager
spec:
  template:
    spec:
      containers:
      - name: manager
        env:
        - name: POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES
          value: "dbp-,meta-"
        - name: POD_RESTART_EXCLUDED_ANNOTATIONS
          value: "custom-annotation"
```

## 🚀 工作原理

### 1. 注解变更检测 
当 InferenceService 的注解发生变更时，系统会：
1. 读取环境变量配置
2. 分析变更的注解是否全部属于排除列表
3. 检查是否存在其他非注解字段的变更

### 2. 智能过滤逻辑
```
如果 (只有排除注解发生变更 && 没有其他字段变更):
    更新 Deployment 注解
    过滤 Pod 模板中的排除注解
    保持 Pod 模板其他部分不变
否则:
    正常更新所有内容（包括 Pod 模板中的所有注解）
```

### 3. 一致性保证机制
- **后续更新同步**：当下次有非注解变更时，会将之前被过滤的注解同步到 Pod 模板
- **状态一致性**：确保 Deployment 和 Pod 模板的注解最终保持一致

## 📝 使用场景

### 场景 1：纯注解更新（避免 Pod 重启）
```yaml
# 更新前
annotations:
  dbp-description: "old-value"
  other-annotation: "unchanged"

# 更新后（只修改 dbp- 注解）
annotations:
  dbp-description: "new-value"  # 只改这个
  other-annotation: "unchanged"
```
**结果**：Deployment 注解更新，Pod 不重启

### 场景 2：混合更新（正常 Pod 重启）
```yaml
# 同时修改注解和其他字段
annotations:
  dbp-description: "new-value"
spec:
  predictor:
    minReplicas: 2  # 修改了非注解字段
```
**结果**：Deployment 和 Pod 模板都正常更新，Pod 重启

### 场景 3：一致性恢复
```yaml
# 步骤1：只更新 dbp 注解（Pod 不重启）
# 步骤2：修改 minReplicas（Pod 重启）
```
**结果**：Pod 重启时，注解会同步到 Pod 模板，保持一致性
