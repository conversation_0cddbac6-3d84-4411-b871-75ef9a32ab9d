# KServe RawDeployment 模式下可配置注解过滤需求文档

## 📋 需求概述

在 KServe RawDeployment 模式下，当 InferenceService 的特定前缀注解（如 `dbp-*`）发生变更时，希望能够：

1. **注解仍然传播到 Deployment 级别**
2. **避免触发 Pod 重启**（不传播到 Pod 模板）
3. **保持数据一致性**（当其他字段变更时，Pod 模板要与 Deployment 保持一致）
4. **支持可配置的注解前缀**（不仅仅是 `dbp-*`）

## 🎯 具体需求

### 当前行为
```yaml
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    dbp-description: "646565"      # 修改这个注解
    dbp-engine-type: sglang        # 或这个注解
    dbp-name: "646565"             # 或这个注解
    dbp-replicas: "1"              # 或这个注解
    serving.kserve.io/deploymentMode: RawDeployment
```

**问题**：修改上述 `dbp-*` 注解时，Pod 会重启

### 期望行为

#### 场景1：只有配置的前缀注解变更
- ✅ 注解传播到 Deployment
- ❌ 注解不传播到 Pod 模板
- ❌ Pod 不重启

#### 场景2：其他字段也发生变更
- ✅ 注解传播到 Deployment
- ✅ 注解传播到 Pod 模板
- ✅ Pod 重启（正常行为）
- ✅ 保持 Deployment 和 Pod 模板一致性

## 🔧 技术要求

### 1. 可配置性
- 通过环境变量配置注解前缀列表和完全匹配的注解名称
- 环境变量：
  - `POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES`：注解前缀列表，用逗号分隔，默认值：`"dbp-"`
  - `POD_RESTART_EXCLUDED_ANNOTATIONS`：完全匹配的注解名称列表，用逗号分隔，默认值：空（不处理）
- 支持多个前缀：`"dbp-,custom-,metadata-"`
- 支持完全匹配的注解名称：`"dbp-eee,custom-eeee"`



### 2. 环境变量配置示例
```bash
# 配置多个前缀
POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES="dbp-,meta-,desc-"

# 配置完全匹配的注解名称
POD_RESTART_EXCLUDED_ANNOTATIONS="exact-annotation,another-exact"

# 只配置前缀（默认情况）
POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES="dbp-"
```

### 3. 实现原则
- **最小侵入性**：通过环境变量配置，不修改现有结构体和接口
- **向后兼容**：不影响现有功能
- **可扩展性**：支持未来添加更多配置



## 🧪 测试场景

### 测试用例1：只有 dbp-* 注解变更
```yaml
# 变更前
annotations:
  dbp-description: "old-value"
  other-annotation: "unchanged"

# 变更后  
annotations:
  dbp-description: "new-value"  # 只改这个
  other-annotation: "unchanged"
```
**期望**：Deployment 更新，Pod 不重启

### 测试用例2：dbp-* 注解 + 其他字段变更
```yaml
# 变更前
annotations:
  dbp-description: "old-value"
spec:
  containers:
  - image: "old-image:v1"

# 变更后
annotations:
  dbp-description: "new-value"
spec:
  containers:
  - image: "new-image:v2"  # 镜像也变了
```
**期望**：Deployment 和 Pod 都更新，Pod 重启

### 测试用例3：注解一致性验证
```yaml
# 步骤1：只更新 dbp-* 注解
annotations:
  dbp-description: "new-value"  # 只改这个
  other-annotation: "unchanged"

# 步骤2：修改 minReplicas 触发 Pod 更新
spec:
  predictor:
    minReplicas: 1  # 添加这个字段
```
**期望**：
- 步骤1后：Deployment 更新，Pod 模板不变
- 步骤2后：Pod 注解与 Deployment 注解保持一致，都包含 `dbp-description: "new-value"`

### 测试用例4：配置多个前缀
```bash
POD_RESTART_EXCLUDED_ANNOTATION_PREFIXES="dbp-,meta-,desc-"
```
**期望**：所有配置的前缀都生效

## 📝 实施步骤建议

1. **第一阶段**：添加环境变量支持
   - 在 DeploymentReconciler 中添加环境变量读取逻辑
   - 实现配置解析函数（支持逗号分隔的字符串）
   - 设置默认值

2. **第二阶段**：实现核心逻辑
   - 实现注解过滤函数
   - 实现智能变更检测
   - 修改 Deployment 创建逻辑

3. **第三阶段**：集成和测试
   - 编写单元测试
   - 编写集成测试

## ⚠️ 注意事项

1. **向后兼容性**：确保现有功能不受影响
2. **性能影响**：注解比较逻辑要高效
3. **边界情况**：处理注解为空、前缀重叠等情况
4. **文档更新**：更新用户文档和配置说明

### 4. 测试要求

1.  **单元测试**:
    - 为 `FilterAnnotationsByPrefix` 辅助函数编写单元测试，覆盖各种边界情况（如 nil map、空前缀列表、前缀重叠等）。
    - 为 `deployment.go` 中的新逻辑编写单元测试，模拟 `InferenceService` 的不同变更场景。

2.  **集成测试 (Controller Test)**:
    - 在 `pkg/controller/v1beta1/inferenceservice/controller_test.go` 中添加新的测试用例。
    - **Case 1**: 模拟只更新 `dbp-*` 注解，并验证 `Deployment` 的 Pod 模板 **没有** 发生变更（通过检查其 `generation` 或 `annotations`）。然后修改 ISVC 的 `minReplicas: 1`，检查 Pod 是否和 Deployment 的注解一样，都包含 `dbp-` 注解。
    - **Case 2**: 模拟同时更新 `dbp-*` 注解和容器镜像，并验证 `Deployment` 的 Pod 模板 **已** 更新。
    - **Case 3**: 模拟更新 ConfigMap 以包含新的前缀，并验证新规则生效。

