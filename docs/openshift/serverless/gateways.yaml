apiVersion: v1
kind: Service
metadata:
  labels:
    experimental.istio.io/disable-gateway-port-translation: "true"
  name: knative-local-gateway
  namespace: istio-system
spec:
  ports:
    - name: http2
      port: 80
      protocol: TCP
      targetPort: 8081
  selector:
    istio: ingressgateway
  type: ClusterIP
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: knative-ingress-gateway
  namespace: knative-serving
spec:
  selector:
    istio: ingressgateway
  servers:
    - hosts:
        - '*'
      port:
        name: https
        number: 443
        protocol: HTTPS
      tls:
        credentialName: wildcard-certs
        mode: SIMPLE
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: knative-local-gateway
  namespace: knative-serving
spec:
  selector:
    istio: ingressgateway
  servers:
    - hosts:
        - '*'
      port:
        name: http
        number: 8081
        protocol: HTTP
---
apiVersion: v1
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMzekNDQWNjQ0FRQXdEUVlKS29aSWh2Y05BUUVMQlFBd0tURVRNQkVHQTFVRUNnd0tRMnhoYzJnZ1NXNWoKTGpFU01CQUdBMVVFQXd3SlkyeGhjMmd1WTI5dE1CNFhEVEl6TURNeE1ERXdNakExT1ZvWERUSTBNRE13T1RFdwpNakExT1Zvd1FqRXJNQ2tHQTFVRUF3d2lLaTVqYkdGemFDNWhjSEJ6TG05d1pXNXphR2xtZEM1bGVHRnRjR3hsCkxtTnZiVEVUTUJFR0ExVUVDZ3dLUTJ4aGMyZ2dTVzVqTGpDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVAKQURDQ0FRb0NnZ0VCQU1QYklqTzIrNFpwN05GZHFIM1c3WTZXb25zMnZWbmJORXBBb2t3OTdIb1Q3R0tyYXA1MQppL0M3N2pyTXdsUnpua2M4MHI1Y3FsWXBLNUd3eml0bEhSWkc5bU5HZzMrKzFRdTk2NjdQV0NRUE1lNzZ2T3VPCnlCdmtvM01wSUI4QkFmZHFJVk45L3BFdWFtdThVZS92U0xTb2hmZk5lSkNyM2oxNzY2MUdFcnVGUjhqRFAxcXEKeDltU09zQ0U0RVJSallJeWE4blFMZE5aMVg4WEYrVGpXekJCdlJ1R3FzS2VZVzNwWEFGamVXaHozcG1aZXovKwphaHljbVM2ckVMU0N1aUIwSU9xZTVsZ0EvcUVoc2xOd2pYSzNvZndicDAxVDJhWFdWenY3K2Z5dURQc3hacktZCnNNMlY3eWx0dlJRZ01YOFZmenEyTkRSeWlnRzU5eWdNQVVFQ0F3RUFBVEFOQmdrcWhraUc5dzBCQVFzRkFBT0MKQVFFQUgxaUUzZmJxY3pQblBITko4WVpyYUtVSUxLd0dDRGRNWllJYlpsK25zSFIyUERSRW5kd3g3T24yengyUwoxTWx1MTYzRjFETlpBVTZZUVp1bGFLN2xrWlpSQllja0xzREcwKzVyb1VxQ2sySU1iaE9FYlE4STNNQi94NytjCkc3NDU0cGZ6YU9nM0hOQlFzSGtzVHN5cUVSQUZranNwQTRBNVhoOUdKMEFrS1d6emZyaGVScGtpOWFzcmhPUjMKUDdJTnR4eDNXbmVrbUJGZzdIYm9pQzZuWXRHUExxMW5sQy84S1lKRk0rYmxpOENHTHVzb2NXS3dzSXkybCtFbQp2UUVLQXMzKzY3SVZ5M0ZtRlFrdFArVzQvQlhuazM0YWZRTUZhZzlnTkdoQVd3elJ6VDNuSmtEN2psWXZmUHZzClNqcUtKU2lONlJRWTBmM2JDNVhNaHlnMFhnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: wildcard-certs
  namespace: istio-system
type: kubernetes.io/tls

