[tool.poetry]
name = "alibiexplainer"
version = "0.15.2"
description = "Model Explanation Server. Not intended for use outside KServe Frameworks Images."
authors = ["clives<PERSON>on <<EMAIL>>"]
license = "https://github.com/kserve/kserve/blob/master/LICENSE"
readme = "README.md"
packages = [
    { include = "alibiexplainer" },
]

[tool.poetry.dependencies]
python = ">=3.9,<=3.12"
kserve = { path = "../../../../../python/kserve", extras = ["storage"], develop = true }
alibi = { version = "^0.9.4", extras = ["shap", "tensorflow"] } # From 0.9.5 alibi uses BSL license
tensorflow = ">=2.14"  # version that starts to support python 3.9
dill = "^0.3.8"
nest-asyncio = "~1.4.0"
llvmlite = ">0.38.1" # needed since poetry chooses lower version of llvmlite which is not supported by python 3.9 above
tensorflow-io-gcs-filesystem = "0.37.1"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^7.2.0"
pytest-cov = "^4.0.0"
mypy = "^0.991"
sklearnserver = { path = "../../../../../python/sklearnserver", develop = true }

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
black = { version = "~24.3.0", extras = ["colorama"] }

[tool.poetry-version-plugin]
source = "file"
file_path = "../../../../../python/VERSION"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
