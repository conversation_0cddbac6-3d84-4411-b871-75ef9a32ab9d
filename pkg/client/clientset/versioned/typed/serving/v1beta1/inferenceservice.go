/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	context "context"

	servingv1beta1 "github.com/kserve/kserve/pkg/apis/serving/v1beta1"
	scheme "github.com/kserve/kserve/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// InferenceServicesGetter has a method to return a InferenceServiceInterface.
// A group's client should implement this interface.
type InferenceServicesGetter interface {
	InferenceServices(namespace string) InferenceServiceInterface
}

// InferenceServiceInterface has methods to work with InferenceService resources.
type InferenceServiceInterface interface {
	Create(ctx context.Context, inferenceService *servingv1beta1.InferenceService, opts v1.CreateOptions) (*servingv1beta1.InferenceService, error)
	Update(ctx context.Context, inferenceService *servingv1beta1.InferenceService, opts v1.UpdateOptions) (*servingv1beta1.InferenceService, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, inferenceService *servingv1beta1.InferenceService, opts v1.UpdateOptions) (*servingv1beta1.InferenceService, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*servingv1beta1.InferenceService, error)
	List(ctx context.Context, opts v1.ListOptions) (*servingv1beta1.InferenceServiceList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *servingv1beta1.InferenceService, err error)
	InferenceServiceExpansion
}

// inferenceServices implements InferenceServiceInterface
type inferenceServices struct {
	*gentype.ClientWithList[*servingv1beta1.InferenceService, *servingv1beta1.InferenceServiceList]
}

// newInferenceServices returns a InferenceServices
func newInferenceServices(c *ServingV1beta1Client, namespace string) *inferenceServices {
	return &inferenceServices{
		gentype.NewClientWithList[*servingv1beta1.InferenceService, *servingv1beta1.InferenceServiceList](
			"inferenceservices",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *servingv1beta1.InferenceService { return &servingv1beta1.InferenceService{} },
			func() *servingv1beta1.InferenceServiceList { return &servingv1beta1.InferenceServiceList{} },
		),
	}
}
