/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	servingv1beta1 "github.com/kserve/kserve/pkg/apis/serving/v1beta1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// InferenceServiceLister helps list InferenceServices.
// All objects returned here must be treated as read-only.
type InferenceServiceLister interface {
	// List lists all InferenceServices in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1beta1.InferenceService, err error)
	// InferenceServices returns an object that can list and get InferenceServices.
	InferenceServices(namespace string) InferenceServiceNamespaceLister
	InferenceServiceListerExpansion
}

// inferenceServiceLister implements the InferenceServiceLister interface.
type inferenceServiceLister struct {
	listers.ResourceIndexer[*servingv1beta1.InferenceService]
}

// NewInferenceServiceLister returns a new InferenceServiceLister.
func NewInferenceServiceLister(indexer cache.Indexer) InferenceServiceLister {
	return &inferenceServiceLister{listers.New[*servingv1beta1.InferenceService](indexer, servingv1beta1.Resource("inferenceservice"))}
}

// InferenceServices returns an object that can list and get InferenceServices.
func (s *inferenceServiceLister) InferenceServices(namespace string) InferenceServiceNamespaceLister {
	return inferenceServiceNamespaceLister{listers.NewNamespaced[*servingv1beta1.InferenceService](s.ResourceIndexer, namespace)}
}

// InferenceServiceNamespaceLister helps list and get InferenceServices.
// All objects returned here must be treated as read-only.
type InferenceServiceNamespaceLister interface {
	// List lists all InferenceServices in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1beta1.InferenceService, err error)
	// Get retrieves the InferenceService from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*servingv1beta1.InferenceService, error)
	InferenceServiceNamespaceListerExpansion
}

// inferenceServiceNamespaceLister implements the InferenceServiceNamespaceLister
// interface.
type inferenceServiceNamespaceLister struct {
	listers.ResourceIndexer[*servingv1beta1.InferenceService]
}
