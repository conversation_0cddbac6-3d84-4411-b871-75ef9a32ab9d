/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	servingv1alpha1 "github.com/kserve/kserve/pkg/apis/serving/v1alpha1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// LocalModelNodeLister helps list LocalModelNodes.
// All objects returned here must be treated as read-only.
type LocalModelNodeLister interface {
	// List lists all LocalModelNodes in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.LocalModelNode, err error)
	// LocalModelNodes returns an object that can list and get LocalModelNodes.
	LocalModelNodes(namespace string) LocalModelNodeNamespaceLister
	LocalModelNodeListerExpansion
}

// localModelNodeLister implements the LocalModelNodeLister interface.
type localModelNodeLister struct {
	listers.ResourceIndexer[*servingv1alpha1.LocalModelNode]
}

// NewLocalModelNodeLister returns a new LocalModelNodeLister.
func NewLocalModelNodeLister(indexer cache.Indexer) LocalModelNodeLister {
	return &localModelNodeLister{listers.New[*servingv1alpha1.LocalModelNode](indexer, servingv1alpha1.Resource("localmodelnode"))}
}

// LocalModelNodes returns an object that can list and get LocalModelNodes.
func (s *localModelNodeLister) LocalModelNodes(namespace string) LocalModelNodeNamespaceLister {
	return localModelNodeNamespaceLister{listers.NewNamespaced[*servingv1alpha1.LocalModelNode](s.ResourceIndexer, namespace)}
}

// LocalModelNodeNamespaceLister helps list and get LocalModelNodes.
// All objects returned here must be treated as read-only.
type LocalModelNodeNamespaceLister interface {
	// List lists all LocalModelNodes in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.LocalModelNode, err error)
	// Get retrieves the LocalModelNode from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*servingv1alpha1.LocalModelNode, error)
	LocalModelNodeNamespaceListerExpansion
}

// localModelNodeNamespaceLister implements the LocalModelNodeNamespaceLister
// interface.
type localModelNodeNamespaceLister struct {
	listers.ResourceIndexer[*servingv1alpha1.LocalModelNode]
}
