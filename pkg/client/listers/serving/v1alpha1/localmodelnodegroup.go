/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	servingv1alpha1 "github.com/kserve/kserve/pkg/apis/serving/v1alpha1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// LocalModelNodeGroupLister helps list LocalModelNodeGroups.
// All objects returned here must be treated as read-only.
type LocalModelNodeGroupLister interface {
	// List lists all LocalModelNodeGroups in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.LocalModelNodeGroup, err error)
	// LocalModelNodeGroups returns an object that can list and get LocalModelNodeGroups.
	LocalModelNodeGroups(namespace string) LocalModelNodeGroupNamespaceLister
	LocalModelNodeGroupListerExpansion
}

// localModelNodeGroupLister implements the LocalModelNodeGroupLister interface.
type localModelNodeGroupLister struct {
	listers.ResourceIndexer[*servingv1alpha1.LocalModelNodeGroup]
}

// NewLocalModelNodeGroupLister returns a new LocalModelNodeGroupLister.
func NewLocalModelNodeGroupLister(indexer cache.Indexer) LocalModelNodeGroupLister {
	return &localModelNodeGroupLister{listers.New[*servingv1alpha1.LocalModelNodeGroup](indexer, servingv1alpha1.Resource("localmodelnodegroup"))}
}

// LocalModelNodeGroups returns an object that can list and get LocalModelNodeGroups.
func (s *localModelNodeGroupLister) LocalModelNodeGroups(namespace string) LocalModelNodeGroupNamespaceLister {
	return localModelNodeGroupNamespaceLister{listers.NewNamespaced[*servingv1alpha1.LocalModelNodeGroup](s.ResourceIndexer, namespace)}
}

// LocalModelNodeGroupNamespaceLister helps list and get LocalModelNodeGroups.
// All objects returned here must be treated as read-only.
type LocalModelNodeGroupNamespaceLister interface {
	// List lists all LocalModelNodeGroups in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.LocalModelNodeGroup, err error)
	// Get retrieves the LocalModelNodeGroup from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*servingv1alpha1.LocalModelNodeGroup, error)
	LocalModelNodeGroupNamespaceListerExpansion
}

// localModelNodeGroupNamespaceLister implements the LocalModelNodeGroupNamespaceLister
// interface.
type localModelNodeGroupNamespaceLister struct {
	listers.ResourceIndexer[*servingv1alpha1.LocalModelNodeGroup]
}
