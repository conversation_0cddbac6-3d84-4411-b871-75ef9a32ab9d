/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	servingv1alpha1 "github.com/kserve/kserve/pkg/apis/serving/v1alpha1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// ClusterStorageContainerLister helps list ClusterStorageContainers.
// All objects returned here must be treated as read-only.
type ClusterStorageContainerLister interface {
	// List lists all ClusterStorageContainers in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.ClusterStorageContainer, err error)
	// ClusterStorageContainers returns an object that can list and get ClusterStorageContainers.
	ClusterStorageContainers(namespace string) ClusterStorageContainerNamespaceLister
	ClusterStorageContainerListerExpansion
}

// clusterStorageContainerLister implements the ClusterStorageContainerLister interface.
type clusterStorageContainerLister struct {
	listers.ResourceIndexer[*servingv1alpha1.ClusterStorageContainer]
}

// NewClusterStorageContainerLister returns a new ClusterStorageContainerLister.
func NewClusterStorageContainerLister(indexer cache.Indexer) ClusterStorageContainerLister {
	return &clusterStorageContainerLister{listers.New[*servingv1alpha1.ClusterStorageContainer](indexer, servingv1alpha1.Resource("clusterstoragecontainer"))}
}

// ClusterStorageContainers returns an object that can list and get ClusterStorageContainers.
func (s *clusterStorageContainerLister) ClusterStorageContainers(namespace string) ClusterStorageContainerNamespaceLister {
	return clusterStorageContainerNamespaceLister{listers.NewNamespaced[*servingv1alpha1.ClusterStorageContainer](s.ResourceIndexer, namespace)}
}

// ClusterStorageContainerNamespaceLister helps list and get ClusterStorageContainers.
// All objects returned here must be treated as read-only.
type ClusterStorageContainerNamespaceLister interface {
	// List lists all ClusterStorageContainers in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.ClusterStorageContainer, err error)
	// Get retrieves the ClusterStorageContainer from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*servingv1alpha1.ClusterStorageContainer, error)
	ClusterStorageContainerNamespaceListerExpansion
}

// clusterStorageContainerNamespaceLister implements the ClusterStorageContainerNamespaceLister
// interface.
type clusterStorageContainerNamespaceLister struct {
	listers.ResourceIndexer[*servingv1alpha1.ClusterStorageContainer]
}
