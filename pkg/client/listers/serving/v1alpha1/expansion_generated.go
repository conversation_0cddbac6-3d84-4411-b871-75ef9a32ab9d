/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

// ClusterServingRuntimeListerExpansion allows custom methods to be added to
// ClusterServingRuntimeLister.
type ClusterServingRuntimeListerExpansion interface{}

// ClusterServingRuntimeNamespaceListerExpansion allows custom methods to be added to
// ClusterServingRuntimeNamespaceLister.
type ClusterServingRuntimeNamespaceListerExpansion interface{}

// ClusterStorageContainerListerExpansion allows custom methods to be added to
// ClusterStorageContainerLister.
type ClusterStorageContainerListerExpansion interface{}

// ClusterStorageContainerNamespaceListerExpansion allows custom methods to be added to
// ClusterStorageContainerNamespaceLister.
type ClusterStorageContainerNamespaceListerExpansion interface{}

// InferenceGraphListerExpansion allows custom methods to be added to
// InferenceGraphLister.
type InferenceGraphListerExpansion interface{}

// InferenceGraphNamespaceListerExpansion allows custom methods to be added to
// InferenceGraphNamespaceLister.
type InferenceGraphNamespaceListerExpansion interface{}

// LocalModelCacheListerExpansion allows custom methods to be added to
// LocalModelCacheLister.
type LocalModelCacheListerExpansion interface{}

// LocalModelCacheNamespaceListerExpansion allows custom methods to be added to
// LocalModelCacheNamespaceLister.
type LocalModelCacheNamespaceListerExpansion interface{}

// LocalModelNodeListerExpansion allows custom methods to be added to
// LocalModelNodeLister.
type LocalModelNodeListerExpansion interface{}

// LocalModelNodeNamespaceListerExpansion allows custom methods to be added to
// LocalModelNodeNamespaceLister.
type LocalModelNodeNamespaceListerExpansion interface{}

// LocalModelNodeGroupListerExpansion allows custom methods to be added to
// LocalModelNodeGroupLister.
type LocalModelNodeGroupListerExpansion interface{}

// LocalModelNodeGroupNamespaceListerExpansion allows custom methods to be added to
// LocalModelNodeGroupNamespaceLister.
type LocalModelNodeGroupNamespaceListerExpansion interface{}

// ServingRuntimeListerExpansion allows custom methods to be added to
// ServingRuntimeLister.
type ServingRuntimeListerExpansion interface{}

// ServingRuntimeNamespaceListerExpansion allows custom methods to be added to
// ServingRuntimeNamespaceLister.
type ServingRuntimeNamespaceListerExpansion interface{}

// TrainedModelListerExpansion allows custom methods to be added to
// TrainedModelLister.
type TrainedModelListerExpansion interface{}

// TrainedModelNamespaceListerExpansion allows custom methods to be added to
// TrainedModelNamespaceLister.
type TrainedModelNamespaceListerExpansion interface{}
