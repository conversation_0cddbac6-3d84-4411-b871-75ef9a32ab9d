/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	servingv1alpha1 "github.com/kserve/kserve/pkg/apis/serving/v1alpha1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// ClusterServingRuntimeLister helps list ClusterServingRuntimes.
// All objects returned here must be treated as read-only.
type ClusterServingRuntimeLister interface {
	// List lists all ClusterServingRuntimes in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.ClusterServingRuntime, err error)
	// ClusterServingRuntimes returns an object that can list and get ClusterServingRuntimes.
	ClusterServingRuntimes(namespace string) ClusterServingRuntimeNamespaceLister
	ClusterServingRuntimeListerExpansion
}

// clusterServingRuntimeLister implements the ClusterServingRuntimeLister interface.
type clusterServingRuntimeLister struct {
	listers.ResourceIndexer[*servingv1alpha1.ClusterServingRuntime]
}

// NewClusterServingRuntimeLister returns a new ClusterServingRuntimeLister.
func NewClusterServingRuntimeLister(indexer cache.Indexer) ClusterServingRuntimeLister {
	return &clusterServingRuntimeLister{listers.New[*servingv1alpha1.ClusterServingRuntime](indexer, servingv1alpha1.Resource("clusterservingruntime"))}
}

// ClusterServingRuntimes returns an object that can list and get ClusterServingRuntimes.
func (s *clusterServingRuntimeLister) ClusterServingRuntimes(namespace string) ClusterServingRuntimeNamespaceLister {
	return clusterServingRuntimeNamespaceLister{listers.NewNamespaced[*servingv1alpha1.ClusterServingRuntime](s.ResourceIndexer, namespace)}
}

// ClusterServingRuntimeNamespaceLister helps list and get ClusterServingRuntimes.
// All objects returned here must be treated as read-only.
type ClusterServingRuntimeNamespaceLister interface {
	// List lists all ClusterServingRuntimes in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*servingv1alpha1.ClusterServingRuntime, err error)
	// Get retrieves the ClusterServingRuntime from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*servingv1alpha1.ClusterServingRuntime, error)
	ClusterServingRuntimeNamespaceListerExpansion
}

// clusterServingRuntimeNamespaceLister implements the ClusterServingRuntimeNamespaceLister
// interface.
type clusterServingRuntimeNamespaceLister struct {
	listers.ResourceIndexer[*servingv1alpha1.ClusterServingRuntime]
}
