/*
Copyright 2023 The KServe Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	internalinterfaces "github.com/kserve/kserve/pkg/client/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// ClusterServingRuntimes returns a ClusterServingRuntimeInformer.
	ClusterServingRuntimes() ClusterServingRuntimeInformer
	// ClusterStorageContainers returns a ClusterStorageContainerInformer.
	ClusterStorageContainers() ClusterStorageContainerInformer
	// InferenceGraphs returns a InferenceGraphInformer.
	InferenceGraphs() InferenceGraphInformer
	// LocalModelCaches returns a LocalModelCacheInformer.
	LocalModelCaches() LocalModelCacheInformer
	// LocalModelNodes returns a LocalModelNodeInformer.
	LocalModelNodes() LocalModelNodeInformer
	// LocalModelNodeGroups returns a LocalModelNodeGroupInformer.
	LocalModelNodeGroups() LocalModelNodeGroupInformer
	// ServingRuntimes returns a ServingRuntimeInformer.
	ServingRuntimes() ServingRuntimeInformer
	// TrainedModels returns a TrainedModelInformer.
	TrainedModels() TrainedModelInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// ClusterServingRuntimes returns a ClusterServingRuntimeInformer.
func (v *version) ClusterServingRuntimes() ClusterServingRuntimeInformer {
	return &clusterServingRuntimeInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// ClusterStorageContainers returns a ClusterStorageContainerInformer.
func (v *version) ClusterStorageContainers() ClusterStorageContainerInformer {
	return &clusterStorageContainerInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// InferenceGraphs returns a InferenceGraphInformer.
func (v *version) InferenceGraphs() InferenceGraphInformer {
	return &inferenceGraphInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// LocalModelCaches returns a LocalModelCacheInformer.
func (v *version) LocalModelCaches() LocalModelCacheInformer {
	return &localModelCacheInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// LocalModelNodes returns a LocalModelNodeInformer.
func (v *version) LocalModelNodes() LocalModelNodeInformer {
	return &localModelNodeInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// LocalModelNodeGroups returns a LocalModelNodeGroupInformer.
func (v *version) LocalModelNodeGroups() LocalModelNodeGroupInformer {
	return &localModelNodeGroupInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// ServingRuntimes returns a ServingRuntimeInformer.
func (v *version) ServingRuntimes() ServingRuntimeInformer {
	return &servingRuntimeInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// TrainedModels returns a TrainedModelInformer.
func (v *version) TrainedModels() TrainedModelInformer {
	return &trainedModelInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
