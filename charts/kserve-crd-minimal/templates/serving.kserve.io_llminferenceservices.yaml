apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.2
  name: llminferenceservices.serving.kserve.io
spec:
  group: serving.kserve.io
  names:
    kind: LLMInferenceService
    listKind: LLMInferenceServiceList
    plural: llminferenceservices
    shortNames:
    - llmisvc
    singular: llminferenceservice
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.url
      name: URL
      type: string
    - jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    - jsonPath: .status.conditions[?(@.type=='Ready')].reason
      name: Reason
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.addresses[*].url
      name: URLs
      priority: 1
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
