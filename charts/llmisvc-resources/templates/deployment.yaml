---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "llm-isvc-resources.deploymentName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "llm-isvc-resources.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    control-plane: {{ include "llm-isvc-resources.deploymentName" . }}
    controller-tools.k8s.io: "1.0"
    {{- with .Values.kserve.llmisvc.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    prometheus.io/scrape: 'true'
    {{- with .Values.kserve.llmisvc.controller.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.kserve.llmisvc.controller.replicas }}
  {{- with .Values.kserve.llmisvc.controller.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "llm-isvc-resources.selectorLabels" . | nindent 6 }}
      control-plane: {{ include "llm-isvc-resources.deploymentName" . }}
      controller-tools.k8s.io: "1.0"
  template:
    metadata:
      labels:
        {{- include "llm-isvc-resources.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: controller
        control-plane: {{ include "llm-isvc-resources.deploymentName" . }}
        controller-tools.k8s.io: "1.0"
        {{- with .Values.kserve.llmisvc.controller.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.commonLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      annotations:
        kubectl.kubernetes.io/default-container: manager
        {{- with .Values.kserve.llmisvc.controller.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.commonAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "llm-isvc-resources.serviceAccountName" . }}
      {{- include "llm-isvc-resources.imagePullSecrets" . | nindent 6 }}
      {{- with .Values.kserve.llmisvc.controller.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.llmisvc.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.llmisvc.controller.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.llmisvc.controller.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.llmisvc.controller.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.kserve.llmisvc.controller.terminationGracePeriodSeconds }}
      containers:
      - name: manager
        image: {{ include "llm-isvc-resources.image" . }}
        imagePullPolicy: {{ include "llm-isvc-resources.imagePullPolicy" . }}
        command:
          - /manager
        {{- with .Values.kserve.llmisvc.controller.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        args:
        - "--metrics-addr={{ .Values.kserve.llmisvc.controller.metricsBindAddress }}:{{ .Values.kserve.llmisvc.controller.metricsBindPort }}"
        - "--leader-elect"
        {{- with .Values.kserve.llmisvc.controller.extraArgs }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          {{- with .Values.kserve.llmisvc.controller.env }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
        {{- if .Values.kserve.llmisvc.controller.livenessProbe.enabled }}
        livenessProbe:
          {{- with .Values.kserve.llmisvc.controller.livenessProbe.httpGet }}
          httpGet:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          initialDelaySeconds: {{ .Values.kserve.llmisvc.controller.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.kserve.llmisvc.controller.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.kserve.llmisvc.controller.livenessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.kserve.llmisvc.controller.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.kserve.llmisvc.controller.readinessProbe.enabled }}
        readinessProbe:
          {{- with .Values.kserve.llmisvc.controller.readinessProbe.httpGet }}
          httpGet:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          initialDelaySeconds: {{ .Values.kserve.llmisvc.controller.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.kserve.llmisvc.controller.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.kserve.llmisvc.controller.readinessProbe.timeoutSeconds }}
          failureThreshold: {{ .Values.kserve.llmisvc.controller.readinessProbe.failureThreshold }}
        {{- end }}
        {{- with .Values.kserve.llmisvc.controller.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        - containerPort: 8443
          name: metrics
          protocol: TCP
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
        {{- with .Values.kserve.llmisvc.controller.extraVolumeMounts }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: kserve-webhook-server-cert
        {{- with .Values.kserve.llmisvc.controller.extraVolumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
