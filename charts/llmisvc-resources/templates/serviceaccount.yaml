---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "llm-isvc-resources.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "llm-isvc-resources.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- with .Values.commonAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- include "llm-isvc-resources.imagePullSecrets" . }}
