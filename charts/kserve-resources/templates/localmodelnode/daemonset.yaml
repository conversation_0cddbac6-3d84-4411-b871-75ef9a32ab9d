{{- if .Values.kserve.localmodel.enabled }}
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kserve-localmodelnode-agent
  namespace:  {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: kserve-localmodelnode-agent
    control-plane: kserve-localmodelnode-agent
    controller-tools.k8s.io: "1.0"
spec:
  selector:
    matchLabels:
      control-plane: kserve-localmodelnode-agent
      controller-tools.k8s.io: "1.0"
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kserve-localmodelnode-agent
        control-plane: kserve-localmodelnode-agent
        controller-tools.k8s.io: "1.0"
      annotations:
        kubectl.kubernetes.io/default-container: manager
    spec:
      {{- with .Values.kserve.localmodel.agent.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.localmodel.agent.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.localmodel.agent.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: kserve-localmodelnode-agent
      securityContext:
        runAsNonRoot: true
      containers:
      - command:
        - /manager
        image: "{{ .Values.kserve.localmodel.agent.image }}:{{ .Values.kserve.localmodel.agent.tag }}"
        imagePullPolicy: IfNotPresent
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsNonRoot: {{ .Values.kserve.localmodel.agent.securityContext.runAsNonRoot }}
          runAsUser: {{ .Values.kserve.localmodel.agent.securityContext.runAsUser }}
        env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
        volumeMounts:
          - mountPath: /mnt/models
            name: models
            readOnly: false
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 200Mi
      volumes:
        - name: models
          hostPath:
            path: {{ .Values.kserve.localmodel.agent.hostPath }}
            type: DirectoryOrCreate
      terminationGracePeriodSeconds: 10
{{- end }}
