---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kserve-controller-manager
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: kserve-controller-manager
    control-plane: kserve-controller-manager
    controller-tools.k8s.io: "1.0"
    {{- if .Values.kserve.controller.labels }}
    {{- toYaml .Values.kserve.controller.labels | nindent 4 }}
    {{- end }}
  annotations:
    prometheus.io/scrape: 'true'
    {{- if .Values.kserve.controller.annotations }}
    {{- toYaml .Values.kserve.controller.annotations | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      control-plane: kserve-controller-manager
      controller-tools.k8s.io: "1.0"
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kserve-controller-manager
        control-plane: kserve-controller-manager
        controller-tools.k8s.io: "1.0"
        {{- if .Values.kserve.controller.podLabels }}
        {{- toYaml .Values.kserve.controller.podLabels | nindent 8 }}
        {{- end }}
      annotations:
        kubectl.kubernetes.io/default-container: manager
        {{- if .Values.kserve.controller.podAnnotations }}
        {{- toYaml .Values.kserve.controller.podAnnotations | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: kserve-controller-manager
      {{- with .Values.kserve.controller.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.controller.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.controller.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kserve.controller.securityContext}}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: kube-rbac-proxy
        image: "{{ .Values.kserve.controller.rbacProxyImage }}"
        args:
        - "--secure-listen-address=0.0.0.0:8443"
        - "--upstream=http://127.0.0.1:8080/"
        - "--logtostderr=true"
        - "--v=10"
        ports:
        - containerPort: 8443
          name: https
        securityContext:
          {{- with .Values.kserve.controller.rbacProxy.securityContext}}
          {{- toYaml . | nindent 10 }}
          {{- end }}
        resources:
          {{- with .Values.kserve.controller.rbacProxy.resources }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
      - command:
        - /manager
        image: "{{ .Values.kserve.controller.image }}:{{ .Values.kserve.controller.tag }}"
        imagePullPolicy: IfNotPresent
        name: manager
        securityContext:
          {{- with .Values.kserve.controller.containerSecurityContext}}
          {{- toYaml . | nindent 10 }}
          {{- end }}
        args:
        - "--metrics-addr={{ .Values.kserve.controller.metricsBindAddress }}:{{ .Values.kserve.controller.metricsBindPort }}"
        - "--leader-elect"
        env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: SECRET_NAME
            value: kserve-webhook-server-cert
        livenessProbe:
          failureThreshold: 5
          initialDelaySeconds: 30
          httpGet:
            path: /healthz
            port: 8081
          timeoutSeconds: 5
        readinessProbe:
          initialDelaySeconds: 30
          failureThreshold: 5
          periodSeconds: 5
          httpGet:
            path: /readyz
            port: 8081
          timeoutSeconds: 5
        resources:
{{- if .Values.kserve.controller.resources }}
{{ toYaml .Values.kserve.controller.resources | trim | indent 12 }}
{{- end }}
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        - containerPort: 8080
          name: metrics
          protocol: TCP
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      terminationGracePeriodSeconds: 10
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: kserve-webhook-server-cert
