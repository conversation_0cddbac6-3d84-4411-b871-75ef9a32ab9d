apiVersion: "serving.kserve.io/v1alpha1"
kind: ClusterStorageContainer
metadata:
  name: default
spec:
  container:
    name: storage-initializer
    image: "{{ .Values.kserve.storage.image }}:{{ .Values.kserve.storage.tag }}"
    resources:
      {{- with .Values.kserve.storage.resources }}
      {{- toYaml . | nindent 6 }}
      {{- end }}
    securityContext:
      {{- with .Values.kserve.storage.containerSecurityContext}}
      {{- toYaml . | nindent 6 }}
      {{- end }}
  supportedUriFormats:
    - prefix: gs://
    - prefix: s3://
    - prefix: hdfs://
    - prefix: hf://
    - prefix: webhdfs://
    - regex: "https://(.+?).blob.core.windows.net/(.+)"
    - regex: "https://(.+?).file.core.windows.net/(.+)"
    - regex: "https?://(.+)/(.+)"
  workloadType: initContainer
