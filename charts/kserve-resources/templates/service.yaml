---
apiVersion: v1
kind: Service
metadata:
  name: kserve-webhook-server-service
  namespace: {{ .Release.Namespace }}
  {{- with .Values.kserve.controller.webhookServiceAnnotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
spec:
  ports:
    - port: 443
      targetPort: webhook-server
  selector:
    control-plane: kserve-controller-manager

---
apiVersion: v1
kind: Service
metadata:
  name: kserve-controller-manager-service
  namespace: {{ .Release.Namespace }}
  labels:
    control-plane: kserve-controller-manager
    controller-tools.k8s.io: "1.0"
  {{- with .Values.kserve.controller.serviceAnnotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
spec:
  selector:
    control-plane: kserve-controller-manager
    controller-tools.k8s.io: "1.0"
  ports:
  - port: 8443
    targetPort: https
    protocol: TCP
