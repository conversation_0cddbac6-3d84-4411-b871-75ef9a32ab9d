apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.2
  name: localmodelcaches.serving.kserve.io
spec:
  group: serving.kserve.io
  names:
    kind: LocalModelCache
    listKind: LocalModelCacheList
    plural: localmodelcaches
    singular: localmodelcache
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              modelSize:
                anyOf:
                - type: integer
                - type: string
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              nodeGroups:
                items:
                  type: string
                minItems: 1
                type: array
              sourceModelUri:
                type: string
                x-kubernetes-validations:
                - message: StorageUri is immutable
                  rule: self == oldSelf
            required:
            - modelSize
            - nodeGroups
            - sourceModelUri
            type: object
          status:
            properties:
              copies:
                properties:
                  available:
                    type: integer
                  failed:
                    type: integer
                  total:
                    type: integer
                type: object
              inferenceServices:
                items:
                  properties:
                    name:
                      type: string
                    namespace:
                      type: string
                  type: object
                type: array
              nodeStatus:
                additionalProperties:
                  enum:
                  - ""
                  - NodeNotReady
                  - NodeDownloadPending
                  - NodeDownloading
                  - NodeDownloaded
                  - NodeDownloadError
                  type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
