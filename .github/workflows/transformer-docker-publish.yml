name: Transformer Docker Publisher

on:
  push:
    # Publish `master` as Docker `latest` image.
    branches:
      - master

  # Run tests for any PRs.
  pull_request:

env:
  IMAGE_NAME: image-transformer
  GRPC_IMAGE_NAME: custom-image-transformer-grpc

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Run tests.
  # See also https://docs.docker.com/docker-hub/builds/automated-testing/
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Run tests
        uses: docker/build-push-action@v5
        with:
          platforms: linux/amd64
          context: python
          file: python/custom_transformer.Dockerfile
          push: false
          # https://github.com/docker/buildx/issues/1533
          provenance: false

  # Push image to GitHub Packages.
  # See also https://docs.docker.com/docker-hub/builds/
  push:
    # Ensure test job passes before pushing image.
    needs: test

    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Export version variable
        run: |
          IMAGE_ID=kserve/$IMAGE_NAME

          # Change all uppercase to lowercase
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')

          # Strip git ref prefix from version
          VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')

          # Strip "v" prefix from tag name
          # [[ "${{ github.ref }}" == "refs/tags/"* ]] && VERSION=$(echo $VERSION | sed -e 's/^v//')

          # Use Docker `latest` tag convention
          [ "$VERSION" == "master" ] && VERSION=latest
     
          echo VERSION=$VERSION >> $GITHUB_ENV
          echo IMAGE_ID=$IMAGE_ID >> $GITHUB_ENV

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64
          context: python
          file: python/custom_transformer.Dockerfile
          push: true
          tags: ${{ env.IMAGE_ID }}:${{ env.VERSION }}
          # https://github.com/docker/buildx/issues/1533
          provenance: false
          sbom: true

  # Run tests.
  # See also https://docs.docker.com/docker-hub/builds/automated-testing/
  test-transformer-grpc:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Run tests
        uses: docker/build-push-action@v5
        with:
          platforms: linux/amd64
          context: python
          file: python/custom_transformer_grpc.Dockerfile
          push: false
          # https://github.com/docker/buildx/issues/1533
          provenance: false

  # Push image to GitHub Packages.
  # See also https://docs.docker.com/docker-hub/builds/
  push-transformer-grpc:
    # Ensure test job passes before pushing image.
    needs: test

    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Export version variable
        run: |
          IMAGE_ID=kserve/$GRPC_IMAGE_NAME

          # Change all uppercase to lowercase
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')

          # Strip git ref prefix from version
          VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')

          # Strip "v" prefix from tag name
          # [[ "${{ github.ref }}" == "refs/tags/"* ]] && VERSION=$(echo $VERSION | sed -e 's/^v//')

          # Use Docker `latest` tag convention
          [ "$VERSION" == "master" ] && VERSION=latest
          
          echo VERSION=$VERSION >> $GITHUB_ENV
          echo IMAGE_ID=$IMAGE_ID >> $GITHUB_ENV

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64
          context: python
          file: python/custom_transformer_grpc.Dockerfile
          push: true
          tags: ${{ env.IMAGE_ID }}:${{ env.VERSION }}
          # https://github.com/docker/buildx/issues/1533
          provenance: false
          sbom: true
