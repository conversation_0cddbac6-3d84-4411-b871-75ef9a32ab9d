# Run specified actions or jobs for issue and PR comments

name: "Prow github actions"
on:
  issue_comment:
    types: [created]

jobs:
  prow-execute:
    runs-on: ubuntu-latest
    steps:
      - uses: jpmcb/prow-github-actions@v2.0.0
        with:
          prow-commands: '/assign 
            /unassign 
            /approve 
            /retitle 
            /area 
            /kind 
            /priority 
            /remove 
            /lgtm 
            /close 
            /reopen 
            /lock 
            /milestone 
            /hold 
            /cc 
            /uncc'
          github-token: "${{ secrets.GITHUB_TOKEN }}"
