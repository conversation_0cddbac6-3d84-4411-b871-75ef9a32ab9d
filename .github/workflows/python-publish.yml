name: Upload Python Package

on:
  release:
    types: [created]

  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install UV
        run: ./test/scripts/gh-actions/setup-uv.sh

      - name: KServe - Build and publish
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          cd python/kserve
          uv build
          uv publish

      - name: KServe Storage - Build and publish
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          cd python/storage
          uv build
          uv publish
