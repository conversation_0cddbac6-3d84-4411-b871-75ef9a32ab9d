# This workflow will remove the lgtm label from a PR that gets updated.
# This prevents any un-reviewed code from being automatically merged by the lgtm-merger mechanism.

name: "Prow remove lgtm label"
on: pull_request

jobs:
  remove-lgtm:
    runs-on: ubuntu-latest
    steps:
      - uses: jpmcb/prow-github-actions@v2.0.0
        with:
          jobs: 'lgtm'
          github-token: "${{ secrets.GITHUB_TOKEN }}"
