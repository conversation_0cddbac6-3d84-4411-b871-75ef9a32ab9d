name: Huggingface vLLM Docker Publisher

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Huggingface vLLM image version to publish'
        required: true

env:
  IMAGE_NAME: huggingfaceserver 

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  push:
    strategy:
      fail-fast: false
      matrix:
        image: 
          - version: ${{ inputs.version }}
            path: 'python/huggingface_server_cpu.Dockerfile'
          - version: ${{ inputs.version }}-gpu
            path: 'python/huggingface_server.Dockerfile'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Free-up disk space
        uses: ./.github/actions/free-up-disk-space

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Export image id and version variable
        run: |
          IMAGE_ID=kserve/$IMAGE_NAME

          # Change all uppercase to lowercase
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
          
          # Add prefix v to version if it doesn't start with v
          if [[ ${{ matrix.image.version }} != v* ]]; then
            VERSION="v${{ matrix.image.version }}"
          else
            VERSION="${{ matrix.image.version }}"
          fi
          
          echo IMAGE_ID=$IMAGE_ID >> $GITHUB_ENV
          echo VERSION=$VERSION >> $GITHUB_ENV

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64
          context: python
          file: ${{ matrix.image.path }}
          push: true
          tags: ${{ env.IMAGE_ID }}:${{ env.VERSION }} 
          # https://github.com/docker/buildx/issues/1533
          provenance: false
          sbom: true
