# This Github workflow will check every hour for PRs with the lgtm label and will attempt to automatically merge them.
# If the hold label is present, it will block automatic merging.

name: "Prow merge on lgtm label"
on:
  schedule:
  - cron: "0 * * * *" # every hour

jobs:
  auto-merge:
    runs-on: ubuntu-latest
    steps:
      - uses: jpmcb/prow-github-actions@v2.0.0
        with:
          jobs: 'lgtm'
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          merge-method: 'squash'
