name: 'Kserve dependency setup action'
description: 'Sets up Kserve dependencies on the running kubernetes cluster'
inputs:
  network-layer:
    description: 'Network layer to install'
    required: true
    default: 'istio'
  deployment-mode:
    description:  'Kserve deployment mode. Supported values are serverless and raw'
    required: false
    default: 'serverless'
  enable-keda:
    description: 'Enable KEDA for autoscaling'
    required: false
    default: 'false'
runs:
  using: "composite"
  steps:
    - name: Setup KServe dependencies
      shell: bash
      run: |
        shopt -s nocasematch 

        if [[ "${{ inputs.network-layer }}" == "kourier" ]]; then
          echo "Selected network layer ${{ inputs.network-layer }}"
          ./test/scripts/gh-actions/setup-kourier.sh
        else
          echo "Selected network layer ${{ inputs.network-layer }}"
          ./test/scripts/gh-actions/setup-deps.sh ${{ inputs.deployment-mode }} "${{ inputs.network-layer }}" "${{ inputs.enable-keda }}"
        fi

    - name: Update test overlays
      shell: bash
      run: |
        ./test/scripts/gh-actions/update-test-overlays.sh
        docker image ls
        cat ./config/overlays/test/configmap/inferenceservice.yaml
